fn(axum::extract::State<ServerState>, axum::<PERSON><PERSON><user::NewUser>, <PERSON><PERSON><PERSON><PERSON>) -> impl std::future::Future<Output = std::result::Result<(reqwest::StatusCode, axum::J<PERSON><user::UserResponse>), (reqwest::StatusCode, std::string::String)>> {user_handlers::create_user}
fn(axum::extract::State<ServerState>, axum::<PERSON><PERSON><user::NewUser>, <PERSON><PERSON>Jar) -> impl std::future::Future<Output = std::result::Result<(reqwest::StatusCode, axum::Json<user::UserResponse>), (reqwest::StatusCode, std::string::String)>> {user_handlers::create_user}: <PERSON>ler<_, _>
