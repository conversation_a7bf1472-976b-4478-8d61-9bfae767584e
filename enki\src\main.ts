import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import { init, router } from './router';
import { firebasePlugin, user } from './firebasePlugin';
import { vPermission } from './directives/vPermission';
import './normalize.css';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import './style.css';
import { useDark } from '@vueuse/core';
import App from './App.vue';

// Use preferred dark mode
useDark();

init(user);

createApp(App)
   .use(createPinia())
   .use(firebasePlugin)
   .use(router)
   .use(ElementPlus)
   .directive('permission', vPermission)
   .mount('#app');