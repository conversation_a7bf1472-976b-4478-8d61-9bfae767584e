use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use axum_extra::extract::cookie::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SameSite};
use chrono::Utc;
use tracing::warn;

use crate::{
    ServerState,
    db::repositories::SessionRepository,
};

// Cookie name for the session token
const SESSION_COOKIE_NAME: &str = "ki_session";

// Session-based authentication middleware
pub async fn session_auth(
    State(state): State<ServerState>,
    jar: <PERSON><PERSON><PERSON><PERSON>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Get session token from cookie
    let session_token = match jar.get(SESSION_COOKIE_NAME) {
        Some(cookie) => cookie.value().to_string(),
        None => {
            warn!("No session cookie found");
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    // Verify session token
    let session_repo = SessionRepository::new(state.db.clone());
    match session_repo.get_by_token(&session_token).await {
        Ok(Some(session)) => {
            // Check if session is expired
            if session.expires_at < Utc::now() {
                warn!("Session expired");
                // Delete expired session
                let _ = session_repo.delete_by_token(&session_token).await;
                return Err(StatusCode::UNAUTHORIZED);
            }

            // Proceed to the next middleware or handler
            Ok(next.run(request).await)
        }
        Ok(None) => {
            warn!("Invalid session token");
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(e) => {
            warn!("Error verifying session: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Helper function to create a session cookie
pub fn create_session_cookie(token: &str) -> Cookie<'static> {
    let mut cookie = Cookie::new(SESSION_COOKIE_NAME, token.to_string());
    cookie.set_http_only(true);
    cookie.set_secure(true); // Only send over HTTPS
    cookie.set_same_site(SameSite::Strict);
    cookie.set_path("/");
    cookie
}

/// Authenticated user context
#[derive(Debug, Clone)]
pub struct AuthenticatedUser {
    pub user_id: String,
    pub firebase_user_id: String,
}

/// Extract authenticated user from request extensions
/// This should be used in handlers after permission middleware has run
pub fn extract_user_from_request(request: &axum::extract::Request) -> Option<AuthenticatedUser> {
    request.extensions().get::<crate::api::middleware::permission_check::UserContext>()
        .map(|ctx| AuthenticatedUser {
            user_id: ctx.user_id.clone(),
            firebase_user_id: ctx.firebase_user_id.clone(),
        })
}

// Extractor to get the AuthenticatedUser in handlers
// We'll use a simpler approach with Extension instead of implementing FromRequestParts
// This avoids lifetime issues with the trait implementation
