use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use axum_extra::extract::cookie::<PERSON>ieJar;
use uuid::Uuid;
use tracing::warn;
use crate::{
    db::{
        models::{NewUser, UpdateUser, UserResponse},
        repositories::{UserRepository, SessionRepository},
    },
    ServerState,
};

/// Helper function to check user permissions
async fn check_user_permission(
    server_state: &ServerState,
    jar: &CookieJar,
    resource_type: &str,
    resource_id: Option<&str>,
    permission: &str,
) -> Result<String, (StatusCode, String)> {
    // Get session token from cookie
    let session_token = match jar.get("ki_session") {
        Some(cookie) => cookie.value().to_string(),
        None => {
            warn!("No session cookie found for permission check");
            return Err((StatusCode::UNAUTHORIZED, "Authentication required".to_string()));
        }
    };

    // Get session from database
    let session_repo = SessionRepository::new(server_state.db.clone());
    let session = match session_repo.get_by_token(&session_token).await {
        Ok(Some(session)) => {
            // Check if session is expired
            if session.expires_at < chrono::Utc::now() {
                warn!("Session expired during permission check");
                return Err((StatusCode::UNAUTHORIZED, "Session expired".to_string()));
            }
            session
        }
        Ok(None) => {
            warn!("Invalid session token during permission check");
            return Err((StatusCode::UNAUTHORIZED, "Invalid session".to_string()));
        }
        Err(e) => {
            warn!("Error verifying session during permission check: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Session verification failed".to_string()));
        }
    };

    // Check permission using SpiceDB
    if let Some(spicedb) = &server_state.spicedb {
        let resource_id_str = resource_id.unwrap_or("system");
        let allowed = match spicedb.check_permission(
            resource_type,
            resource_id_str,
            permission,
            &session.user_id,
        ).await {
            Ok(allowed) => allowed,
            Err(e) => {
                warn!("Error checking permission: {}", e);
                return Err((StatusCode::INTERNAL_SERVER_ERROR, "Permission check failed".to_string()));
            }
        };

        if !allowed {
            warn!(
                "Permission denied: user={}, resource={}:{}, permission={}",
                session.user_id, resource_type, resource_id_str, permission
            );
            return Err((StatusCode::FORBIDDEN, "Insufficient permissions".to_string()));
        }

        Ok(session.user_id)
    } else {
        warn!("SpiceDB service not available for permission check");
        Err((StatusCode::SERVICE_UNAVAILABLE, "Permission service unavailable".to_string()))
    }
}

/// Get all users with permission checks
pub async fn get_users_secure(
    State(server_state): State<ServerState>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<Vec<UserResponse>>), (StatusCode, String)> {
    // Check permission to view users
    let _user_id = check_user_permission(
        &server_state,
        &jar,
        "global_settings",
        Some("system"),
        "manage_users",
    ).await?;

    let repo = UserRepository::new(server_state.db);

    match repo.get_all().await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter().map(|user| user.into()).collect();
            Ok((StatusCode::OK, Json(user_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a user by ID with permission checks
pub async fn get_user_secure(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    // Check permission to view this specific user
    let _requesting_user_id = check_user_permission(
        &server_state,
        &jar,
        "user",
        Some(&id),
        "view",
    ).await?;

    let repo = UserRepository::new(server_state.db);

    match repo.get_by_id(user_id).await {
        Ok(Some(user)) => Ok((StatusCode::OK, Json(user.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a new user with permission checks
pub async fn create_user_secure(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    // Check permission to create users
    let creator_user_id = check_user_permission(
        &server_state,
        &jar,
        "global_settings",
        Some("system"),
        "manage_users",
    ).await?;

    let repo = UserRepository::new(server_state.db.clone());

    match repo.create(new_user.clone()).await {
        Ok(user) => {
            // Grant the creator view permission on the new user
            if let Some(spicedb) = &server_state.spicedb {
                if let Err(e) = spicedb.grant_resource_permission(
                    "user",
                    &user.id.to_string(),
                    "view",
                    &creator_user_id,
                ).await {
                    warn!("Failed to grant creator permission: {}", e);
                    // Continue anyway - the user was created successfully
                }
            }
            
            Ok((StatusCode::CREATED, Json(user.into())))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a user with permission checks
pub async fn update_user_secure(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_user): Json<UpdateUser>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    // Check permission to edit this specific user
    let _requesting_user_id = check_user_permission(
        &server_state,
        &jar,
        "user",
        Some(&id),
        "edit",
    ).await?;

    let repo = UserRepository::new(server_state.db);

    match repo.update(user_id, update_user).await {
        Ok(user) => Ok((StatusCode::OK, Json(user.into()))),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, "User not found".to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete a user with permission checks
pub async fn delete_user_secure(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    jar: CookieJar,
) -> Result<StatusCode, (StatusCode, String)> {
    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    // Check permission to delete this specific user
    let _requesting_user_id = check_user_permission(
        &server_state,
        &jar,
        "user",
        Some(&id),
        "delete",
    ).await?;

    let repo = UserRepository::new(server_state.db);

    match repo.delete(user_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Example of batch permission checking for multiple users
pub async fn get_users_batch_check(
    State(server_state): State<ServerState>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<Vec<UserResponse>>), (StatusCode, String)> {
    // Get current user from session
    let session_token = match jar.get("ki_session") {
        Some(cookie) => cookie.value().to_string(),
        None => return Err((StatusCode::UNAUTHORIZED, "Authentication required".to_string())),
    };

    let session_repo = SessionRepository::new(server_state.db.clone());
    let session = match session_repo.get_by_token(&session_token).await {
        Ok(Some(session)) => session,
        _ => return Err((StatusCode::UNAUTHORIZED, "Invalid session".to_string())),
    };

    // Get all users first
    let repo = UserRepository::new(server_state.db.clone());
    let users = match repo.get_all().await {
        Ok(users) => users,
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    };

    // Batch check permissions for each user
    if let Some(spicedb) = &server_state.spicedb {
        let checks: Vec<crate::db::models::PermissionCheck> = users.iter().map(|user| {
            crate::db::models::PermissionCheck {
                resource_type: "user".to_string(),
                resource_id: user.id.to_string(),
                permission: "view".to_string(),
                subject_type: "user".to_string(),
                subject_id: session.user_id.clone(),
            }
        }).collect();

        match spicedb.batch_check_permissions_optimized(checks).await {
            Ok(results) => {
                // Filter users based on permission results
                let allowed_users: Vec<UserResponse> = users.into_iter()
                    .zip(results.iter())
                    .filter_map(|(user, result)| {
                        if result.allowed {
                            Some(user.into())
                        } else {
                            None
                        }
                    })
                    .collect();

                Ok((StatusCode::OK, Json(allowed_users)))
            }
            Err(e) => {
                warn!("Batch permission check failed: {}", e);
                Err((StatusCode::INTERNAL_SERVER_ERROR, "Permission check failed".to_string()))
            }
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "Permission service unavailable".to_string()))
    }
}
