<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh, Connection, Warning } from '@element-plus/icons-vue';
import { getSyncStatus, triggerSync, getSpiceDBHealth } from '@/services/kiApi';
import { useSpacesStore } from '@/stores/spacesStore';
import type { SyncStatus, HealthStatus } from '@/types';

const spacesStore = useSpacesStore();

const syncStatus = ref<SyncStatus | null>(null);
const healthStatus = ref<HealthStatus | null>(null);
const loading = ref(false);
const syncing = ref(false);
const error = ref<string | null>(null);

const loadSyncStatus = async () => {
    const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
    if (!server) {
        error.value = 'No server configured for current space';
        return;
    }

    loading.value = true;
    error.value = null;

    try {
        const [syncData, healthData] = await Promise.all([
            getSyncStatus(server),
            getSpiceDBHealth(server)
        ]);
        
        syncStatus.value = syncData;
        healthStatus.value = healthData;
    } catch (err: any) {
        error.value = err.message;
        console.error('Failed to load sync status:', err);
    } finally {
        loading.value = false;
    }
};

const handleTriggerSync = async () => {
    const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
    if (!server) {
        ElMessage.error('No server configured for current space');
        return;
    }

    syncing.value = true;
    
    try {
        const updatedStatus = await triggerSync(server);
        syncStatus.value = updatedStatus;
        ElMessage.success('Synchronization completed successfully');
        
        // Refresh health status
        const healthData = await getSpiceDBHealth(server);
        healthStatus.value = healthData;
    } catch (err: any) {
        ElMessage.error(`Synchronization failed: ${err.message}`);
        console.error('Sync failed:', err);
    } finally {
        syncing.value = false;
    }
};

onMounted(() => {
    loadSyncStatus();
});

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
};

const getHealthStatusColor = (healthy: boolean) => {
    return healthy ? 'success' : 'danger';
};

const getHealthStatusText = (healthy: boolean) => {
    return healthy ? 'Healthy' : 'Unhealthy';
};
</script>

<template>
    <el-card class="sync-status-panel">
        <template #header>
            <div class="panel-header">
                <div class="header-info">
                    <h4>Database Synchronization</h4>
                    <p class="header-description">
                        Sync status between SQLite and SpiceDB permissions
                    </p>
                </div>
                <div class="header-actions">
                    <el-button 
                        :icon="Refresh" 
                        @click="loadSyncStatus"
                        :loading="loading"
                        size="small"
                    >
                        Refresh
                    </el-button>
                    <el-button 
                        type="primary"
                        :icon="Connection" 
                        @click="handleTriggerSync"
                        :loading="syncing"
                        size="small"
                    >
                        Sync Now
                    </el-button>
                </div>
            </div>
        </template>

        <div v-if="loading && !syncStatus" class="loading-container">
            <el-skeleton :rows="4" animated />
        </div>

        <div v-else-if="error" class="error-container">
            <el-alert
                :title="error"
                type="error"
                :icon="Warning"
                show-icon
                :closable="false"
            />
        </div>

        <div v-else class="sync-content">
            <!-- SpiceDB Health Status -->
            <div class="health-section">
                <h5>SpiceDB Health</h5>
                <div class="health-status">
                    <el-tag 
                        :type="getHealthStatusColor(healthStatus?.healthy || false)"
                        size="large"
                        effect="dark"
                    >
                        {{ getHealthStatusText(healthStatus?.healthy || false) }}
                    </el-tag>
                    <span v-if="healthStatus" class="health-timestamp">
                        Last checked: {{ formatDate(healthStatus.timestamp) }}
                    </span>
                </div>
                <div v-if="healthStatus?.error" class="health-error">
                    <el-text type="danger">{{ healthStatus.error }}</el-text>
                </div>
            </div>

            <!-- Sync Statistics -->
            <div v-if="syncStatus" class="sync-stats">
                <h5>Synchronization Statistics</h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ syncStatus.user_count }}</div>
                        <div class="stat-label">Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ syncStatus.agent_count }}</div>
                        <div class="stat-label">Agents</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ syncStatus.role_count }}</div>
                        <div class="stat-label">Roles</div>
                    </div>
                </div>
                <div class="last-sync">
                    <el-text type="info">
                        Last sync: {{ formatDate(syncStatus.last_sync) }}
                    </el-text>
                </div>
            </div>

            <!-- Sync Actions -->
            <div class="sync-actions">
                <el-alert
                    title="Synchronization Info"
                    type="info"
                    :closable="false"
                    show-icon
                >
                    <template #default>
                        <p>This synchronizes your SQLite database with SpiceDB permissions:</p>
                        <ul>
                            <li>Users get self-view permissions</li>
                            <li>Agents get creator relationships</li>
                            <li>Default admin permissions are set up</li>
                            <li>All entities are registered in SpiceDB</li>
                        </ul>
                    </template>
                </el-alert>
            </div>
        </div>
    </el-card>
</template>

<style scoped>
.sync-status-panel {
    margin-bottom: 20px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-info h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.header-description {
    margin: 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.loading-container,
.error-container {
    padding: 20px 0;
}

.sync-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.health-section h5,
.sync-stats h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
}

.health-status {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.health-timestamp {
    font-size: 12px;
    color: var(--el-text-color-secondary);
}

.health-error {
    margin-top: 8px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 12px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-color-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.last-sync {
    text-align: center;
}

.sync-actions :deep(.el-alert__content) {
    line-height: 1.5;
}

.sync-actions ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.sync-actions li {
    margin-bottom: 4px;
}
</style>
