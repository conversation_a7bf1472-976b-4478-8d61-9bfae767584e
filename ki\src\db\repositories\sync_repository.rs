use anyhow::Result;
use uuid::Uuid;
use crate::db::{
    connection::{Database, DatabaseConnection},
    models::sync::{
        RoleMembership, NewRoleMembership, EntityPermission, NewEntityPermission, SyncStatus,
    },
};

/// Repository for synchronization-related database operations
pub struct SyncRepository {
    db: Database,
}

impl SyncRepository {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    // Role Membership operations
    pub async fn create_role_membership(&self, membership: NewRoleMembership) -> Result<RoleMembership> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    "INSERT INTO role_memberships (id, role_id, member_id, member_type) VALUES (?, ?, ?, ?)"
                )
                .bind(id)
                .bind(&membership.role_id)
                .bind(&membership.member_id)
                .bind(&membership.member_type)
                .execute(pool)
                .await?;

                // Fetch the created record
                let result = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE id = ?"
                )
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query_as::<_, RoleMembership>(
                    "INSERT INTO role_memberships (id, role_id, member_id, member_type) VALUES ($1, $2, $3, $4) RETURNING id, role_id, member_id, member_type, created_at, updated_at"
                )
                .bind(id)
                .bind(&membership.role_id)
                .bind(&membership.member_id)
                .bind(&membership.member_type)
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn delete_role_membership(&self, role_id: &str, member_id: &str, member_type: &str) -> Result<bool> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let result = sqlx::query(
                    "DELETE FROM role_memberships WHERE role_id = ? AND member_id = ? AND member_type = ?"
                )
                .bind(role_id)
                .bind(member_id)
                .bind(member_type)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query(
                    "DELETE FROM role_memberships WHERE role_id = $1 AND member_id = $2 AND member_type = $3"
                )
                .bind(role_id)
                .bind(member_id)
                .bind(member_type)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn get_role_memberships(&self, role_id: &str) -> Result<Vec<RoleMembership>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = ?"
                )
                .bind(role_id)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = $1"
                )
                .bind(role_id)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn get_member_roles(&self, member_id: &str, member_type: &str) -> Result<Vec<RoleMembership>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = ? AND member_type = ?"
                )
                .bind(member_id)
                .bind(member_type)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = $1 AND member_type = $2"
                )
                .bind(member_id)
                .bind(member_type)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn clear_role_memberships(&self, role_id: &str) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM role_memberships WHERE role_id = ?")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query("DELETE FROM role_memberships WHERE role_id = $1")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[allow(unreachable_patterns)]
            _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
        }

        Ok(())
    }

    // Entity Permission operations (unified for users and agents)
    pub async fn create_entity_permission(&self, permission: NewEntityPermission) -> Result<EntityPermission> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    "INSERT INTO entity_permissions (id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(id)
                .bind(&permission.entity_id)
                .bind(&permission.entity_type)
                .bind(&permission.resource_type)
                .bind(&permission.resource_id)
                .bind(&permission.permission)
                .bind(&permission.granted_via)
                .bind(&permission.granted_via_id)
                .execute(pool)
                .await?;

                // Fetch the created record
                let result = sqlx::query_as::<_, EntityPermission>(
                    "SELECT id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at FROM entity_permissions WHERE id = ?"
                )
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query_as::<_, EntityPermission>(
                    "INSERT INTO entity_permissions (id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at"
                )
                .bind(id)
                .bind(&permission.entity_id)
                .bind(&permission.entity_type)
                .bind(&permission.resource_type)
                .bind(&permission.resource_id)
                .bind(&permission.permission)
                .bind(&permission.granted_via)
                .bind(&permission.granted_via_id)
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn delete_user_permission(
        &self,
        user_id: &str,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        granted_via: &str,
        granted_via_id: Option<&str>,
    ) -> Result<bool> {
        self.delete_entity_permission(user_id, "user", resource_type, resource_id, permission, granted_via, granted_via_id).await
    }

    pub async fn delete_agent_permission(
        &self,
        agent_id: &str,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        granted_via: &str,
        granted_via_id: Option<&str>,
    ) -> Result<bool> {
        self.delete_entity_permission(agent_id, "agent", resource_type, resource_id, permission, granted_via, granted_via_id).await
    }

    pub async fn get_entity_permissions(&self, entity_id: &str, entity_type: &str) -> Result<Vec<EntityPermission>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let permissions = sqlx::query_as::<_, EntityPermission>(
                    "SELECT id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at FROM entity_permissions WHERE entity_id = ? AND entity_type = ?"
                )
                .bind(entity_id)
                .bind(entity_type)
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permissions = sqlx::query_as::<_, EntityPermission>(
                    "SELECT id, entity_id, entity_type, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at FROM entity_permissions WHERE entity_id = $1 AND entity_type = $2"
                )
                .bind(entity_id)
                .bind(entity_type)
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn clear_entity_permissions(&self, entity_id: &str, entity_type: &str) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM entity_permissions WHERE entity_id = ? AND entity_type = ?")
                    .bind(entity_id)
                    .bind(entity_type)
                    .execute(pool)
                    .await?;
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query("DELETE FROM entity_permissions WHERE entity_id = $1 AND entity_type = $2")
                    .bind(entity_id)
                    .bind(entity_type)
                    .execute(pool)
                    .await?;
            }
            #[allow(unreachable_patterns)]
            _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
        }

        Ok(())
    }

    // Convenience methods for backward compatibility
    pub async fn create_user_permission(&self, user_id: &str, resource_type: &str, resource_id: &str, permission: &str, granted_via: &str, granted_via_id: Option<&str>) -> Result<EntityPermission> {
        let new_permission = NewEntityPermission {
            entity_id: user_id.to_string(),
            entity_type: "user".to_string(),
            resource_type: resource_type.to_string(),
            resource_id: resource_id.to_string(),
            permission: permission.to_string(),
            granted_via: granted_via.to_string(),
            granted_via_id: granted_via_id.map(|s| s.to_string()),
        };
        self.create_entity_permission(new_permission).await
    }

    pub async fn create_agent_permission(&self, agent_id: &str, resource_type: &str, resource_id: &str, permission: &str, granted_via: &str, granted_via_id: Option<&str>) -> Result<EntityPermission> {
        let new_permission = NewEntityPermission {
            entity_id: agent_id.to_string(),
            entity_type: "agent".to_string(),
            resource_type: resource_type.to_string(),
            resource_id: resource_id.to_string(),
            permission: permission.to_string(),
            granted_via: granted_via.to_string(),
            granted_via_id: granted_via_id.map(|s| s.to_string()),
        };
        self.create_entity_permission(new_permission).await
    }

    pub async fn get_user_permissions(&self, user_id: &str) -> Result<Vec<EntityPermission>> {
        self.get_entity_permissions(user_id, "user").await
    }

    pub async fn get_agent_permissions(&self, agent_id: &str) -> Result<Vec<EntityPermission>> {
        self.get_entity_permissions(agent_id, "agent").await
    }

    pub async fn clear_user_permissions(&self, user_id: &str) -> Result<()> {
        self.clear_entity_permissions(user_id, "user").await
    }

    pub async fn clear_agent_permissions(&self, agent_id: &str) -> Result<()> {
        self.clear_entity_permissions(agent_id, "agent").await
    }

    pub async fn delete_entity_permission(
        &self,
        entity_id: &str,
        entity_type: &str,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        granted_via: &str,
        granted_via_id: Option<&str>,
    ) -> Result<bool> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let result = sqlx::query(
                    "DELETE FROM entity_permissions WHERE entity_id = ? AND entity_type = ? AND resource_type = ? AND resource_id = ? AND permission = ? AND granted_via = ? AND (granted_via_id = ? OR (granted_via_id IS NULL AND ? IS NULL))"
                )
                .bind(entity_id)
                .bind(entity_type)
                .bind(resource_type)
                .bind(resource_id)
                .bind(permission)
                .bind(granted_via)
                .bind(granted_via_id)
                .bind(granted_via_id)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query(
                    "DELETE FROM entity_permissions WHERE entity_id = $1 AND entity_type = $2 AND resource_type = $3 AND resource_id = $4 AND permission = $5 AND granted_via = $6 AND (granted_via_id = $7 OR (granted_via_id IS NULL AND $7 IS NULL))"
                )
                .bind(entity_id)
                .bind(entity_type)
                .bind(resource_type)
                .bind(resource_id)
                .bind(permission)
                .bind(granted_via)
                .bind(granted_via_id)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    // Bulk operations for performance
    pub async fn clear_all_role_permissions(&self, role_id: &str) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM entity_permissions WHERE granted_via = 'role' AND granted_via_id = ?")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query("DELETE FROM entity_permissions WHERE granted_via = 'role' AND granted_via_id = $1")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[allow(unreachable_patterns)]
            _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
        }

        Ok(())
    }

    pub async fn get_sync_status(&self) -> Result<SyncStatus> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let role_memberships_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM role_memberships")
                    .fetch_one(pool)
                    .await?;

                let entity_permissions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM entity_permissions")
                    .fetch_one(pool)
                    .await?;

                let user_permissions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM entity_permissions WHERE entity_type = 'user'")
                    .fetch_one(pool)
                    .await?;

                let agent_permissions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM entity_permissions WHERE entity_type = 'agent'")
                    .fetch_one(pool)
                    .await?;

                Ok(SyncStatus {
                    last_sync: chrono::Utc::now(),
                    role_memberships_count,
                    user_permissions_count,
                    agent_permissions_count,
                    spicedb_healthy: true, // This will be updated by the sync service
                    sync_errors: vec![],
                })
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let role_memberships_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM role_memberships")
                    .fetch_one(pool)
                    .await?;

                let user_permissions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM entity_permissions WHERE entity_type = 'user'")
                    .fetch_one(pool)
                    .await?;

                let agent_permissions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM entity_permissions WHERE entity_type = 'agent'")
                    .fetch_one(pool)
                    .await?;

                Ok(SyncStatus {
                    last_sync: chrono::Utc::now(),
                    role_memberships_count,
                    user_permissions_count,
                    agent_permissions_count,
                    spicedb_healthy: true, // This will be updated by the sync service
                    sync_errors: vec![],
                })
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
