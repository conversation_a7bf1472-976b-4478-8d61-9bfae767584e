import Node from 'element-plus/es/components/tree/src/model/node';

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Column = {
	id: TaskStatus;
	name: string;
}

export type TaskStatus = 'todo' | 'doing' | 'review' | 'done';

export type Task = {
	id: string;
	name: string;
	status: TaskStatus;
	description?: string;
	tags: string[];
	assignees: string[];
	start_time?: string;
	end_time?: string;
	parent_task_id: string | undefined;
	position: number;
	blocking: string[];
	blocked_by: string[];
	created_at?: string;
	updated_at?: string;
}

export type Project = Task & { parent_task_id: undefined };

export type SpaceDefinition = {
    name: string;
    server: string;
}

export type Space = {
    projects: Project[];
}

export type TaskUpdateResponse = {
    primary_task: Task;
    affected_tasks: Task[];
}

export type User = {
    id: string;
    user_id: string;
    email: string;
    display_name?: string;
    photo_url?: string;
    created_at: string;
    updated_at: string;
    last_login?: string;
}

export type Agent = {
    id: string;
    name: string;
    description?: string;
    parameters: AgentParameters;
    created_by: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
}

export type AgentParameters = {
    model_type: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
    tools: string[];
    custom_config: Record<string, any>;
}

export type Permission = {
    id: string;
    entity_type: 'user' | 'agent';
    entity_id: string;
    resource_type: 'task' | 'project' | 'user' | 'agent' | 'system';
    resource_id?: string;
    permission_level: 'read' | 'write' | 'admin';
    granted_by: string;
    created_at: string;
}

// Role-related types
export type Role = {
    id: string;
    name: string;
    description?: string;
    color?: string;
    created_at: string;
    updated_at: string;
}

export type NewRole = {
    name: string;
    description?: string;
    color?: string;
}

export type UpdateRole = {
    name?: string;
    description?: string;
    color?: string;
}

export type RoleNodeData = {
    id: string;
    name: string;
    description?: string;
    color?: string;
    member_count: number;
}

export type RoleTreeNode = Node & {
    data: RoleNodeData
}

export interface RoleMember {
    id: string;
    name?: string;
    email?: string;
    avatar_url?: string;
    type: 'user' | 'agent';
}

export type RoleChild = {
    id: string;
    name: string;
    member_count: number;
}

export interface RoleDetails {
    id: string;
    name: string;
    description?: string;
    color?: string;
    permissions: string[];
    members: RoleMember[];
    created_at: string;
    updated_at: string;
}

export interface RolePermission {
    resource_type: string;
    resource_id: string;
    permission: string;
}

export interface EditRolePermissionsRequest {
    add: RolePermission[];
    remove: RolePermission[];
}

export type SetRoleParentRequest = {
    parent_id?: string;
}

export type RelationshipOperation = 'create' | 'delete';

export type RelationshipTuple = {
    resource_type: string;
    resource_id: string;
    relation: string;
    subject_type: string;
    subject_id: string;
}

export type BulkRelationshipOperation = {
    operation: RelationshipOperation;
    relationships: RelationshipTuple[];
}

export type PermissionCheck = {
    resource_type: string;
    resource_id: string;
    permission: string;
    subject_type: string;
    subject_id: string;
}

export type PermissionCheckRequest = {
    checks: PermissionCheck[];
}

export type PermissionCheckResult = {
    resource_type: string;
    resource_id: string;
    permission: string;
    subject_type: string;
    subject_id: string;
    allowed: boolean;
}

export type PermissionCheckResponse = {
    results: PermissionCheckResult[];
}

export type SyncStatus = {
    user_count: number;
    agent_count: number;
    role_count: number;
    spicedb_healthy: boolean;
    last_sync: string;
}

export type HealthStatus = {
    healthy: boolean;
    service: string;
    timestamp: string;
    error?: string;
}