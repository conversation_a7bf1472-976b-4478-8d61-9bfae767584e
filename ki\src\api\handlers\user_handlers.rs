use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use axum_extra::extract::cookie::<PERSON><PERSON><PERSON><PERSON>;
use uuid::Uuid;
use tracing::warn;
use crate::{
    db::{
        models::{<PERSON><PERSON>ser, NewManualUser, UpdateUser, UserResponse},
        repositories::{UserRepository, SessionRepository},
    },
    services::user_service::UserService,
    ServerState,
};

/// Helper function to check user permissions
async fn check_user_permission(
    server_state: &ServerState,
    jar: &CookieJar,
    resource_type: &str,
    resource_id: Option<&str>,
    permission: &str,
) -> Result<String, (StatusCode, String)> {
    // Get session token from cookie
    let session_token = match jar.get("ki_session") {
        Some(cookie) => cookie.value().to_string(),
        None => {
            warn!("No session cookie found for permission check");
            return Err((StatusCode::UNAUTHORIZED, "Authentication required".to_string()));
        }
    };

    // Get session from database
    let session_repo = SessionRepository::new(server_state.db.clone());
    let session = match session_repo.get_by_token(&session_token).await {
        Ok(Some(session)) => {
            // Check if session is expired
            if session.expires_at < chrono::Utc::now() {
                warn!("Session expired during permission check");
                return Err((StatusCode::UNAUTHORIZED, "Session expired".to_string()));
            }
            session
        }
        Ok(None) => {
            warn!("Invalid session token during permission check");
            return Err((StatusCode::UNAUTHORIZED, "Invalid session".to_string()));
        }
        Err(e) => {
            warn!("Error verifying session during permission check: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Session verification failed".to_string()));
        }
    };

    // Check permission using SpiceDB
    if let Some(spicedb) = &server_state.spicedb {
        let resource_id_str = resource_id.unwrap_or("system");
        let allowed = match spicedb.check_permission(
            resource_type,
            resource_id_str,
            permission,
            &session.user_id,
        ).await {
            Ok(allowed) => allowed,
            Err(e) => {
                warn!("Error checking permission: {}", e);
                return Err((StatusCode::INTERNAL_SERVER_ERROR, "Permission check failed".to_string()));
            }
        };

        if !allowed {
            warn!(
                "Permission denied: user={}, resource={}:{}, permission={}",
                session.user_id, resource_type, resource_id_str, permission
            );
            return Err((StatusCode::FORBIDDEN, "Insufficient permissions".to_string()));
        }

        Ok(session.user_id)
    } else {
        warn!("SpiceDB service not available for permission check");
        Err((StatusCode::SERVICE_UNAVAILABLE, "Permission service unavailable".to_string()))
    }
}

/// Get all users (original handler without permission checks)
pub async fn get_users(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<Vec<UserResponse>>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.get_all().await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter().map(|user| user.into()).collect();
            Ok((StatusCode::OK, Json(user_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get all users with permission checks
pub async fn get_users_with_permissions(
    State(server_state): State<ServerState>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<Vec<UserResponse>>), (StatusCode, String)> {
    // Check permission to view users
    let _user_id = check_user_permission(
        &server_state,
        &jar,
        "global_settings",
        Some("system"),
        "manage_users",
    ).await?;

    let repo = UserRepository::new(server_state.db);

    match repo.get_all().await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter().map(|user| user.into()).collect();
            Ok((StatusCode::OK, Json(user_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a user by ID
pub async fn get_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.get_by_id(user_id).await {
        Ok(Some(user)) => Ok((StatusCode::OK, Json(user.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a user by Firebase user ID
pub async fn get_user_by_firebase_id(
    State(server_state): State<ServerState>,
    Path(firebase_user_id): Path<String>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.get_by_user_id(&firebase_user_id).await {
        Ok(Some(user)) => Ok((StatusCode::OK, Json(user.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a new user
pub async fn create_user(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.create(new_user).await {
        Ok(user) => Ok((StatusCode::CREATED, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a user
pub async fn update_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_user): Json<UpdateUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.update(user_id, update_user).await {
        Ok(user) => Ok((StatusCode::OK, Json(user.into()))),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, "User not found".to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete a user
pub async fn delete_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.delete(user_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create or update user (upsert)
pub async fn upsert_user(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.upsert(new_user).await {
        Ok(user) => Ok((StatusCode::OK, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a manual user (without Firebase ID)
pub async fn create_manual_user(
    State(server_state): State<ServerState>,
    Json(manual_user): Json<NewManualUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let user_service = UserService::new(server_state.db);

    match user_service.create_manual_user(
        manual_user.email,
        manual_user.display_name,
        manual_user.photo_url,
    ).await {
        Ok(user) => Ok((StatusCode::CREATED, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}
