use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::{
    // db::models::{
    //     PermissionSummary, BulkSyncRequest, BulkSyncResponse,
    //     EnrichedRoleMembership, SyncResult,
    // },
    services::sync_service::{SyncService, SyncStatus},
    ServerState,
};

/// Sync status response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatusResponse {
    pub user_count: usize,
    pub agent_count: usize,
    pub role_count: usize,
    pub spicedb_healthy: bool,
    pub last_sync: String,
}

impl From<SyncStatus> for SyncStatusResponse {
    fn from(status: SyncStatus) -> Self {
        Self {
            user_count: status.user_count,
            agent_count: status.agent_count,
            role_count: status.role_count,
            spicedb_healthy: status.spicedb_healthy,
            last_sync: status.last_sync.to_rfc3339(),
        }
    }
}

/// Get synchronization status
pub async fn get_sync_status(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<SyncStatusResponse>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());
        
        match sync_service.get_sync_status().await {
            Ok(status) => Ok((StatusCode::OK, Json(status.into()))),
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Trigger manual synchronization with options
pub async fn trigger_sync(
    State(server_state): State<ServerState>,
    // Json(request): Json<BulkSyncRequest>,
) -> Result<(StatusCode, Json<SyncStatusResponse>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());

        // Perform synchronization
        match sync_service.sync_all().await {
            Ok(()) => {
                // Return updated status
                match sync_service.get_sync_status().await {
                    Ok(status) => Ok((StatusCode::OK, Json(status.into()))),
                    Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
                }
            }
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Health check for SpiceDB connectivity
pub async fn spicedb_health(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<serde_json::Value>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());
        
        let healthy = sync_service.health_check().await;
        
        let response = serde_json::json!({
            "healthy": healthy,
            "service": "SpiceDB",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });
        
        if healthy {
            Ok((StatusCode::OK, Json(response)))
        } else {
            Ok((StatusCode::SERVICE_UNAVAILABLE, Json(response)))
        }
    } else {
        let response = serde_json::json!({
            "healthy": false,
            "service": "SpiceDB",
            "error": "Service not configured",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });
        
        Ok((StatusCode::SERVICE_UNAVAILABLE, Json(response)))
    }
}

/*
/// Get role membership data from synchronized SQLite tables
pub async fn get_role_members_from_sync(
    State(server_state): State<ServerState>,
    Path(role_id): Path<String>,
) -> Result<(StatusCode, Json<Vec<EnrichedRoleMembership>>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());

        // Get role memberships from SQLite
        let sync_repo = crate::db::repositories::SyncRepository::new(server_state.db.clone());
        match sync_repo.get_role_memberships(&role_id).await {
            Ok(memberships) => {
                let mut enriched_memberships = Vec::new();

                for membership in memberships {
                    // Get additional member details
                    let (member_name, member_email, member_avatar_url) =
                        get_member_details(&server_state, &membership.member_id, &membership.member_type).await;

                    // Get role details
                    let (role_name, role_description, role_color) =
                        get_role_details(&server_state, &membership.role_id).await;

                    enriched_memberships.push(EnrichedRoleMembership {
                        role_id: membership.role_id,
                        role_name,
                        role_description,
                        role_color,
                        member_id: membership.member_id,
                        member_type: membership.member_type,
                        member_name,
                        member_email,
                        member_avatar_url,
                        created_at: membership.created_at.to_rfc3339(),
                    });
                }

                Ok((StatusCode::OK, Json(enriched_memberships)))
            }
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Get user permission summary from synchronized SQLite tables
pub async fn get_user_permissions_summary(
    State(server_state): State<ServerState>,
    Path(user_id): Path<String>,
) -> Result<(StatusCode, Json<PermissionSummary>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());

        match sync_service.get_user_permission_summary(&user_id).await {
            Ok(summary) => Ok((StatusCode::OK, Json(summary))),
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Get agent permission summary from synchronized SQLite tables
pub async fn get_agent_permissions_summary(
    State(server_state): State<ServerState>,
    Path(agent_id): Path<String>,
) -> Result<(StatusCode, Json<PermissionSummary>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());

        match sync_service.get_agent_permission_summary(&agent_id).await {
            Ok(summary) => Ok((StatusCode::OK, Json(summary))),
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

// Helper functions

async fn get_member_details(
    server_state: &ServerState,
    member_id: &str,
    member_type: &str,
) -> (Option<String>, Option<String>, Option<String>) {
    match member_type {
        "user" => {
            let user_repo = crate::db::repositories::user_repository::UserRepository::new(server_state.db.clone());
            if let Ok(Some(user)) = user_repo.get_by_user_id(member_id).await {
                (user.display_name, Some(user.email), user.photo_url)
            } else {
                (None, None, None)
            }
        }
        "agent" => {
            if let Ok(agent_uuid) = Uuid::parse_str(member_id) {
                let agent_repo = crate::db::repositories::agent_repository::AgentRepository::new(server_state.db.clone());
                if let Ok(Some(agent)) = agent_repo.get_by_id(agent_uuid).await {
                    (Some(agent.name), None, None)
                } else {
                    (None, None, None)
                }
            } else {
                (None, None, None)
            }
        }
        _ => (None, None, None),
    }
}

async fn get_role_details(
    server_state: &ServerState,
    role_id: &str,
) -> (String, Option<String>, Option<String>) {
    let role_repo = crate::db::repositories::role_repository::RoleRepository::new(server_state.db.clone());

    if let Ok(Some(role)) = role_repo.get_by_name(role_id).await {
        (role.name, role.description, role.color)
    } else {
        // Fallback for default roles
        match role_id {
            "owner" => ("Owner".to_string(), Some("Full system ownership".to_string()), Some("#dc2626".to_string())),
            "member" => ("Member".to_string(), Some("Standard member access".to_string()), Some("#2563eb".to_string())),
            _ => (role_id.to_string(), None, None),
        }
    }
}
*/
