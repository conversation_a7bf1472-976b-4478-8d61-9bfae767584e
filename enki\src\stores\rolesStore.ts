import { defineStore } from "pinia";
import { ref } from "vue";
import type { Role, NewRole, UpdateRole, RoleTreeNode, RoleDetails, RolePermission, EditRolePermissionsRequest } from "@/types";
import { 
    getAllRoles,
    createRole, 
    updateRole, 
    deleteRole, 
    getRoleTree, 
    getRoleDetails,
    setRoleParent,
    getRolePermissions,
    editRolePermissions
} from "@/services/kiApi";
import { useSpacesStore } from "./spacesStore";

export const useRolesStore = defineStore('roles', {
    state: () => ({
        roles: ref<Role[]>([]),
        roleTree: ref<RoleTreeNode[]>([]),
        roleDetails: ref<Record<string, RoleDetails>>({}),
        rolePermissions: ref<Record<string, RolePermission[]>>({}),
        loading: ref(false),
        error: ref<string | null>(null)
    }),
    
    getters: {
        getRole: (state) => (id: string) => {
            return state.roles.find(role => role.id === id);
        },
        
        getRoleByName: (state) => (name: string) => {
            return state.roles.find(role => role.name === name);
        },
        
        getRoleDetails: (state) => (id: string) => {
            return state.roleDetails[id];
        },
    },
    
    actions: {
        async loadRoles() {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                this.roles = await getAllRoles(server);
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to load roles:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async loadRoleTree() {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                this.roleTree = await getRoleTree(server);
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to load role tree:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async loadRoleDetails(roleId: string) {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                const details = await getRoleDetails(server, roleId);
                this.roleDetails[roleId] = details;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to load role details:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async createRole(newRole: NewRole): Promise<Role | null> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return null;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                const role = await createRole(server, newRole);
                this.roles.push(role);
                return role;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to create role:', error);
                return null;
            } finally {
                this.loading = false;
            }
        },
        
        async updateRole(roleId: string, updates: UpdateRole): Promise<Role | null> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return null;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                const role = await updateRole(server, roleId, updates);
                const index = this.roles.findIndex(r => r.id === roleId);
                if (index !== -1) {
                    this.roles[index] = role;
                }
                return role;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to update role:', error);
                return null;
            } finally {
                this.loading = false;
            }
        },
        
        async deleteRole(roleId: string): Promise<boolean> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return false;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                await deleteRole(server, roleId);
                this.roles = this.roles.filter(r => r.id !== roleId);
                delete this.roleDetails[roleId];
                return true;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to delete role:', error);
                return false;
            } finally {
                this.loading = false;
            }
        },
        
        async setRoleParent(roleId: string, parentId?: string): Promise<boolean> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return false;
            }
            
            this.loading = true;
            this.error = null;
            
            try {
                await setRoleParent(server, roleId, { parent_id: parentId });
                // Reload tree to reflect changes
                await this.loadRoleTree();
                return true;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to set role parent:', error);
                return false;
            } finally {
                this.loading = false;
            }
        },

        async loadRolePermissions(roleId: string): Promise<RolePermission[] | null> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return null;
            }

            this.loading = true;
            this.error = null;

            try {
                const perms = await getRolePermissions(server, roleId);
                this.rolePermissions[roleId] = perms;
                return perms;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to load role permissions:', error);
                return null;
            } finally {
                this.loading = false;
            }
        },

        async editRolePermissions(roleId: string, request: EditRolePermissionsRequest): Promise<boolean> {
            const spacesStore = useSpacesStore();
            const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
            if (!server) {
                this.error = 'No server configured for current space';
                return false;
            }

            this.loading = true;
            this.error = null;

            try {
                await editRolePermissions(server, roleId, request);
                await this.loadRolePermissions(roleId);
                return true;
            } catch (error: any) {
                this.error = error.message;
                console.error('Failed to edit role permissions:', error);
                return false;
            } finally {
                this.loading = false;
            }
        },
        
        clearError() {
            this.error = null;
        }
    }
});
