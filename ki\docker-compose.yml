version: '3.8'

services:
  ki:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./.config:/app/.config
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=./app/.config/gcloud/application_default_credentials.json
      - DATABASE_URL=sqlite:/app/data/ki.db
      - PORT=3000
      - RUST_LOG=trace
      - RUST_BACKTRACE=full
      - SPICEDB_ENDPOINT=http://spicedb:50051
      - SPICEDB_PRESHARED_KEY=somerandomkeyhere
      # For production mTLS (uncomment and provide certificate paths):
      # - SPICEDB_CERT_PATH=/app/certs/client.crt
      # - SPICEDB_KEY_PATH=/app/certs/client.key
      # - SPICEDB_CA_PATH=/app/certs/ca.crt
    depends_on:
      - spicedb

  spicedb:
    image: "quay.io/authzed/spicedb:v1.30.0"
    command: "serve"
    restart: "always"
    ports:
      - "8080:8080"
      - "9090:9090"
      - "50051:50051"
    environment:
      - "SPICEDB_GRPC_PRESHARED_KEY=somerandomkeyhere"
      - "SPICEDB_DATASTORE_ENGINE=memory"
      - "SPICEDB_LOG_LEVEL=debug"
