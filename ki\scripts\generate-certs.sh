#!/bin/bash

# Generate mTLS certificates for SpiceDB production deployment
# This script creates a Certificate Authority (CA) and client certificates for secure communication

set -e

CERT_DIR="./certs"
CA_KEY="$CERT_DIR/ca.key"
CA_CERT="$CERT_DIR/ca.crt"
CLIENT_KEY="$CERT_DIR/client.key"
CLIENT_CERT="$CERT_DIR/client.crt"
CLIENT_CSR="$CERT_DIR/client.csr"

# Create certificates directory
mkdir -p "$CERT_DIR"

echo "Generating mTLS certificates for SpiceDB..."

# Generate CA private key
echo "1. Generating Certificate Authority (CA) private key..."
openssl genrsa -out "$CA_KEY" 4096

# Generate CA certificate
echo "2. Generating Certificate Authority (CA) certificate..."
openssl req -new -x509 -key "$CA_KEY" -sha256 -subj "/C=US/ST=CA/O=Ki/CN=SpiceDB-CA" -days 3650 -out "$CA_CERT"

# Generate client private key
echo "3. Generating client private key..."
openssl genrsa -out "$CLIENT_KEY" 4096

# Generate client certificate signing request
echo "4. Generating client certificate signing request..."
openssl req -new -key "$CLIENT_KEY" -out "$CLIENT_CSR" -subj "/C=US/ST=CA/O=Ki/CN=ki-client"

# Generate client certificate signed by CA
echo "5. Generating client certificate signed by CA..."
openssl x509 -req -in "$CLIENT_CSR" -CA "$CA_CERT" -CAkey "$CA_KEY" -CAcreateserial -out "$CLIENT_CERT" -days 365 -sha256

# Clean up CSR file
rm "$CLIENT_CSR"

# Set appropriate permissions
chmod 600 "$CA_KEY" "$CLIENT_KEY"
chmod 644 "$CA_CERT" "$CLIENT_CERT"

echo "✅ mTLS certificates generated successfully!"
echo ""
echo "Files created:"
echo "  - CA Certificate: $CA_CERT"
echo "  - CA Private Key: $CA_KEY"
echo "  - Client Certificate: $CLIENT_CERT"
echo "  - Client Private Key: $CLIENT_KEY"
echo ""
echo "To use these certificates:"
echo "1. Configure SpiceDB to use the CA certificate for client verification"
echo "2. Set environment variables:"
echo "   export SPICEDB_CERT_PATH=$PWD/$CLIENT_CERT"
echo "   export SPICEDB_KEY_PATH=$PWD/$CLIENT_KEY"
echo "   export SPICEDB_CA_PATH=$PWD/$CA_CERT"
echo ""
echo "⚠️  Keep these certificates secure and never commit them to version control!"
