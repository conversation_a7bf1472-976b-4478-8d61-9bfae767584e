use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use uuid::Uuid;
use crate::{
    db::{
        models::{
            NewRole, UpdateRole, RoleResponse, RoleDetails, RoleMember, RoleTreeNode,
            SetRoleParentRequest, BulkRelationshipOperation, PermissionCheckRequest,
            PermissionCheckResponse, RolePermission, EditRolePermissionsRequest,
        },
        repositories::{
            role_repository::RoleRepository,
            user_repository::UserRepository,
            agent_repository::AgentRepository,
        },
    },

    ServerState,
};

/// Create a new role
pub async fn create_role(
    State(server_state): State<ServerState>,
    Json(new_role): Json<NewRole>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let repo = RoleRepository::new(server_state.db.clone());

    // Check if role name already exists
    match repo.exists_by_name(&new_role.name).await {
        Ok(true) => return Err((StatusCode::CONFLICT, "Role name already exists".to_string())),
        Ok(false) => {},
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }

    match repo.create(new_role).await {
        Ok(role) => Ok((StatusCode::CREATED, Json(role.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get all roles
pub async fn get_roles(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<Vec<RoleResponse>>), (StatusCode, String)> {
    let repo = RoleRepository::new(server_state.db.clone());

    match repo.get_all().await {
        Ok(roles) => {
            let role_responses: Vec<RoleResponse> = roles.into_iter().map(|role| role.into()).collect();
            Ok((StatusCode::OK, Json(role_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get role by ID
pub async fn get_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    match repo.get_by_id(role_id).await {
        Ok(Some(role)) => Ok((StatusCode::OK, Json(role.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update role
pub async fn update_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_role): Json<UpdateRole>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    // Check if new name already exists (if name is being updated)
    if let Some(ref name) = update_role.name {
        match repo.exists_by_name(name).await {
            Ok(true) => {
                // Check if it's the same role
                match repo.get_by_id(role_id).await {
                    Ok(Some(existing_role)) => {
                        if existing_role.name != *name {
                            return Err((StatusCode::CONFLICT, "Role name already exists".to_string()));
                        }
                    }
                    Ok(None) => return Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
                    Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
                }
            }
            Ok(false) => {},
            Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    }

    match repo.update(role_id, update_role).await {
        Ok(Some(role)) => Ok((StatusCode::OK, Json(role.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Delete role
pub async fn delete_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    match repo.delete(role_id).await {
        Ok(true) => Ok(StatusCode::NO_CONTENT),
        Ok(false) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get role tree (hierarchy)
pub async fn get_role_tree(
    State(_server_state): State<ServerState>,
) -> Result<(StatusCode, Json<Vec<RoleTreeNode>>), (StatusCode, String)> {
    // TODO: Implement SpiceDB integration to build role tree
    tracing::info!("get_role_tree called - placeholder implementation");
    Ok((StatusCode::OK, Json(vec![])))
}

/// Get role details with members
pub async fn get_role_details(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<RoleDetails>), (StatusCode, String)> {
    let role_uuid = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let role_repo = RoleRepository::new(server_state.db.clone());
    let user_repo = UserRepository::new(server_state.db.clone());
    let agent_repo = AgentRepository::new(server_state.db.clone());

    let role = match role_repo.get_by_id(role_uuid).await {
        Ok(Some(r)) => r,
        Ok(None) => return Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    };

    let spicedb = match &server_state.spicedb {
        Some(s) => s,
        None => return Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string())),
    };

    // For default roles (owner, member), use the role name; for custom roles, use the role name from DB
    let role_identifier = if id == "owner" || id == "member" {
        id.clone()
    } else {
        role.name.to_lowercase()
    };

    let subjects = match spicedb.get_role_members(&role_identifier).await {
        Ok(m) => m,
        Err(e) => {
            tracing::warn!("Failed to get role members for {}: {}", role_identifier, e);
            // Return empty members list instead of failing
            vec![]
        }
    };

    let mut members = Vec::new();
    for subj in subjects {
        match subj.member_type.as_str() {
            "user" => {
                // The subj.id is the Firebase user ID, not the database UUID
                // We need to find the user by Firebase user ID (user_id field)
                if let Ok(Some(u)) = user_repo.get_by_user_id(&subj.id).await {
                    members.push(RoleMember {
                        id: subj.id,
                        name: u.display_name.clone(),
                        email: Some(u.email),
                        avatar_url: u.photo_url.clone(),
                        member_type: "user".to_string(),
                    });
                } else {
                    tracing::warn!("User not found in database for Firebase ID: {}", subj.id);
                }
            }
            "agent" => {
                if let Ok(uuid) = Uuid::parse_str(&subj.id) {
                    if let Ok(Some(a)) = agent_repo.get_by_id(uuid).await {
                        members.push(RoleMember {
                            id: subj.id,
                            name: Some(a.name),
                            email: None,
                            avatar_url: None,
                            member_type: "agent".to_string(),
                        });
                    }
                }
            }
            _ => {}
        }
    }

    let details = RoleDetails {
        id: role.id.to_string(),
        name: role.name,
        description: role.description,
        color: role.color,
        permissions: vec![],
        members,
        created_at: role.created_at.to_rfc3339(),
        updated_at: role.updated_at.to_rfc3339(),
    };

    Ok((StatusCode::OK, Json(details)))
}

/// Set role parent
pub async fn set_role_parent(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(request): Json<SetRoleParentRequest>,
) -> Result<StatusCode, (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        match spicedb.set_role_parent(&id, request.parent_id.as_deref()).await {
            Ok(()) => {
                tracing::info!("Successfully set role parent: role={}, parent={:?}", id, request.parent_id);
                Ok(StatusCode::OK)
            }
            Err(e) => {
                tracing::error!("Failed to set role parent: {}", e);
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    } else {
        tracing::warn!("SpiceDB not available for role parent operation");
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Bulk relationship operations
pub async fn bulk_relationships(
    State(server_state): State<ServerState>,
    Json(operations): Json<Vec<BulkRelationshipOperation>>,
) -> Result<StatusCode, (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        match spicedb.bulk_write_relationships(operations).await {
            Ok(()) => {
                tracing::info!("Successfully executed bulk relationship operations");
                Ok(StatusCode::OK)
            }
            Err(e) => {
                tracing::error!("Failed to execute bulk relationship operations: {}", e);
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    } else {
        tracing::warn!("SpiceDB not available for bulk relationship operations");
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Check permissions
pub async fn check_permissions(
    State(server_state): State<ServerState>,
    Json(request): Json<PermissionCheckRequest>,
) -> Result<(StatusCode, Json<PermissionCheckResponse>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        match spicedb.batch_check_permissions(request.checks).await {
            Ok(results) => {
                tracing::info!("Successfully checked {} permissions", results.len());
                let response = PermissionCheckResponse { results };
                Ok((StatusCode::OK, Json(response)))
            }
            Err(e) => {
                tracing::error!("Failed to check permissions: {}", e);
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    } else {
        tracing::warn!("SpiceDB not available for permission checking");
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Get all permissions for a role
pub async fn get_role_permissions(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<Vec<RolePermission>>), (StatusCode, String)> {
    let spicedb = match &server_state.spicedb {
        Some(s) => s,
        None => return Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string())),
    };

    match spicedb.get_role_permissions(&id).await {
        Ok(perms) => Ok((StatusCode::OK, Json(perms))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Modify permissions for a role
pub async fn edit_role_permissions(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(request): Json<EditRolePermissionsRequest>,
) -> Result<StatusCode, (StatusCode, String)> {
    let spicedb = match &server_state.spicedb {
        Some(s) => s,
        None => return Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string())),
    };

    match spicedb.modify_role_permissions(&id, request.add, request.remove).await {
        Ok(()) => Ok(StatusCode::OK),
        Err(e) => Err((StatusCode::BAD_REQUEST, e.to_string())),
    }
}
