<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElTree, type TreeNodeData } from 'element-plus';
import { Edit, Delete, User } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import type { Role } from '@/types';
import Node from 'element-plus/es/components/tree/src/model/node';

const props = defineProps<{
    tree: Node[];
    canManage: boolean;
}>();

const emit = defineEmits<{
    'edit-role': [role: Role];
    'delete-role': [role: Role];
    'manage-members': [role: Role];
}>();

const rolesStore = useRolesStore();

// Tree reference
const treeRef = ref<InstanceType<typeof ElTree>>();

// Computed properties
const hasRootNodes = computed(() => props.tree.length > 0);

// Tree props configuration
const treeProps = {
    children: 'children',
    label: 'name'
};

// Drag and drop handlers for el-tree
const allowDrop = (draggingNode: Node, dropNode: Node, type: 'prev' | 'next' | 'inner') => {
    if (!props.canManage) return false;

    // Only allow dropping as child (inner) to create parent-child relationships
    if (type !== 'inner') return false;

    // Prevent dropping on self
    if (draggingNode.data.id === dropNode.data.id) return false;

    // Prevent creating cycles by checking if dropNode is a descendant of draggingNode
    return !isDescendant(draggingNode.data, dropNode.data);
};

const allowDrag = (_: Node) => {
    return props.canManage;
};

const handleNodeDrop = async (draggingNode: Node, dropNode: Node, dropType: 'before' | 'after' | 'inner') => {
    if (!props.canManage || dropType !== 'inner') return;

    try {
        const success = await rolesStore.setRoleParent(draggingNode.data.id, dropNode.data.id);
        if (success) {
            ElMessage.success('Role hierarchy updated successfully');
        }
    } catch (error) {
        console.error('Failed to update role hierarchy:', error);
        ElMessage.error('Failed to update role hierarchy');
    }
};

// Utility functions
const isDescendant = (ancestor: TreeNodeData, node: TreeNodeData): boolean => {
    const checkChildren = (children: TreeNodeData[]): boolean => {
        for (const child of children) {
            if (child.id === ancestor.id) return true;
            if (checkChildren(child.children)) return true;
        }
        return false;
    };

    return checkChildren(node.getChildren());
};

const getRoleColor = (color?: string) => {
    return color || '#6b7280';
};

// Event handlers
const handleEditRole = (data: TreeNodeData) => {
    const role = rolesStore.getRole(data.id);
    if (role) {
        emit('edit-role', role);
    }
};

const handleDeleteRole = (data: TreeNodeData) => {
    const role = rolesStore.getRole(data.id);
    if (role) {
        emit('delete-role', role);
    }
};

const handleManageMembers = (data: TreeNodeData) => {
    const role = rolesStore.getRole(data.id);
    if (role) {
        emit('manage-members', role);
    }
};
</script>

<template>
    <div class="role-hierarchy-tree">
        <div v-if="!hasRootNodes" class="empty-state">
            <p>No roles found. Create your first role to get started.</p>
        </div>

        <div v-else class="tree-container">
            <div class="tree-instructions" v-if="canManage">
                <el-alert
                    title="Drag and drop roles to reorganize the hierarchy"
                    type="info"
                    :closable="false"
                    show-icon
                />
            </div>

            <el-tree
                ref="treeRef"
                :data="tree"
                :props="treeProps"
                node-key="id"
                :draggable="canManage"
                :allow-drop="allowDrop"
                :allow-drag="allowDrag"
                @node-drop="handleNodeDrop"
                class="role-tree"
            >
                <template #default="{ data }">
                    <div class="tree-node-content">
                        <div class="node-info">
                            <div class="role-indicator">
                                <div
                                    class="role-color"
                                    :style="{ backgroundColor: getRoleColor(data.color) }"
                                ></div>
                            </div>
                            <div class="role-details">
                                <div class="role-name">{{ data.name }}</div>
                                <div class="role-meta">
                                    <span class="member-count">{{ data.member_count }} members</span>
                                    <span v-if="data.description" class="role-description">
                                        {{ data.description }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="node-actions" v-if="canManage">
                            <el-button-group size="small">
                                <el-button
                                    :icon="User"
                                    @click.stop="handleManageMembers(data)"
                                    title="Manage Members"
                                />
                                <el-button
                                    :icon="Edit"
                                    @click.stop="handleEditRole(data)"
                                    title="Edit Role"
                                />
                                <el-button
                                    :icon="Delete"
                                    type="danger"
                                    @click.stop="handleDeleteRole(data)"
                                    title="Delete Role"
                                />
                            </el-button-group>
                        </div>
                    </div>
                </template>
            </el-tree>
        </div>
    </div>
</template>



<style scoped>
.role-hierarchy-tree {
    padding: 16px;
}

.empty-state {
    text-align: center;
    padding: 48px 16px;
    color: var(--el-text-color-regular);
}

.tree-instructions {
    margin-bottom: 16px;
}

.role-tree {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    background: var(--el-bg-color);
}

.tree-node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.tree-node-content:hover {
    background-color: var(--el-fill-color-light);
}

.node-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.role-indicator {
    display: flex;
    align-items: center;
}

.role-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.role-details {
    flex: 1;
}

.role-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
}

.role-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.member-count {
    font-weight: 500;
}

.role-description {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.node-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tree-node-content:hover .node-actions {
    opacity: 1;
}

/* Override el-tree default styles */
:deep(.el-tree-node__content) {
    padding: 0 !important;
    height: auto !important;
}

:deep(.el-tree-node__expand-icon) {
    padding: 6px;
}

:deep(.el-tree-node.is-drop-inner > .el-tree-node__content) {
    background-color: var(--el-color-primary-light-9);
    border: 2px dashed var(--el-color-primary);
}

:deep(.el-tree-node.is-dragging > .el-tree-node__content) {
    opacity: 0.5;
}
</style>
