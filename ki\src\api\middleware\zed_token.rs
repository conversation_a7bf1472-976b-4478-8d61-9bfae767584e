use axum::{
    extract::Request,
    http::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON>},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;
use tokio::sync::RwLock;

/// ZedToken for read-after-write consistency
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ZedToken(pub String);

/// ZedToken context for request lifecycle
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct ZedTokenContext {
    /// Latest token received during this request
    pub latest_token: Arc<RwLock<Option<ZedToken>>>,
}

impl ZedTokenContext {
    pub fn new() -> Self {
        Self {
            latest_token: Arc::new(RwLock::new(None)),
        }
    }

    /// Update the latest token
    pub async fn update_token(&self, token: Option<String>) {
        if let Some(token_str) = token {
            let mut latest = self.latest_token.write().await;
            *latest = Some(ZedToken(token_str));
        }
    }

    /// Get the latest token
    pub async fn get_token(&self) -> Option<ZedToken> {
        let latest = self.latest_token.read().await;
        latest.clone()
    }
}

/// Middleware to manage ZedToken lifecycle
/// 
/// This middleware:
/// 1. Creates a ZedTokenContext for each request
/// 2. Ensures tokens are cleaned up after request completion
/// 3. Strips ZedTokens from outgoing external requests for security
pub async fn zed_token_middleware(
    mut request: Request,
    next: Next,
) -> Response {
    // Create a new ZedToken context for this request
    let context = ZedTokenContext::new();
    
    // Insert the context into request extensions
    request.extensions_mut().insert(context.clone());
    
    // Process the request
    let mut response = next.run(request).await;
    
    // Strip any ZedToken headers from the response for security
    // (ZedTokens should never be exposed to external clients)
    strip_zed_token_headers(response.headers_mut());
    
    // The ZedTokenContext will be automatically dropped when this function ends,
    // ensuring cleanup even if there are panics or errors
    
    response
}

/// Strip ZedToken-related headers from outgoing responses
fn strip_zed_token_headers(headers: &mut HeaderMap) {
    // Remove any headers that might contain ZedTokens
    // These are internal SpiceDB consistency tokens that should never be exposed
    let headers_to_remove = [
        "x-spicedb-token",
        "x-zed-token", 
        "spicedb-token",
        "zed-token",
    ];
    
    for header_name in headers_to_remove {
        if let Ok(header) = HeaderName::try_from(header_name) {
            headers.remove(&header);
        }
    }
}

/// Helper function to get ZedTokenContext from request extensions
pub fn get_zed_token_context(request: &Request) -> Option<ZedTokenContext> {
    request.extensions().get::<ZedTokenContext>().cloned()
}

/// Helper trait to add ZedToken management to requests
pub trait ZedTokenRequestExt {
    fn zed_token_context(&self) -> Option<ZedTokenContext>;
}

impl ZedTokenRequestExt for Request {
    fn zed_token_context(&self) -> Option<ZedTokenContext> {
        get_zed_token_context(self)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_strip_zed_token_headers() {
        let mut headers = HeaderMap::new();

        // Add some headers including ZedToken headers
        headers.insert("content-type", HeaderValue::from_static("application/json"));
        headers.insert("x-spicedb-token", HeaderValue::from_static("some-token"));
        headers.insert("x-zed-token", HeaderValue::from_static("another-token"));
        headers.insert("authorization", HeaderValue::from_static("Bearer token"));

        strip_zed_token_headers(&mut headers);

        // ZedToken headers should be removed
        assert!(!headers.contains_key("x-spicedb-token"));
        assert!(!headers.contains_key("x-zed-token"));

        // Other headers should remain
        assert!(headers.contains_key("content-type"));
        assert!(headers.contains_key("authorization"));
    }

    #[tokio::test]
    async fn test_zed_token_context() {
        let context = ZedTokenContext::new();
        
        // Initially no token
        assert!(context.get_token().await.is_none());
        
        // Update token
        context.update_token(Some("test-token".to_string())).await;
        
        // Should have token now
        let token = context.get_token().await;
        assert!(token.is_some());
        assert_eq!(token.unwrap().0, "test-token");
        
        // Update with None should not change existing token
        context.update_token(None).await;
        let token = context.get_token().await;
        assert!(token.is_some());
        assert_eq!(token.unwrap().0, "test-token");
        
        // Update with new token
        context.update_token(Some("new-token".to_string())).await;
        let token = context.get_token().await;
        assert!(token.is_some());
        assert_eq!(token.unwrap().0, "new-token");
    }
}
