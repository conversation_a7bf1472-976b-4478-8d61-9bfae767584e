<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import type { Role, RolePermission } from '@/types';

const props = defineProps<{ modelValue: boolean; role?: Role | null }>();
const emit = defineEmits<{ 'update:modelValue': [boolean]; 'close': [] }>();

const rolesStore = useRolesStore();

const dialogVisible = computed({
    get: () => props.modelValue,
    set: v => emit('update:modelValue', v)
});

const permissions = computed(() => {
    return props.role ? rolesStore.rolePermissions[props.role.id] || [] : [];
});

const loading = ref(false);
const resourceType = ref<'task' | 'global_settings'>('task');
const resourceId = ref('');
const permission = ref('viewer');

const permOptions: Record<string, string[]> = {
    task: ['viewer', 'editor', 'admin', 'owner'],
    global_settings: ['owner', 'admin']
};

watch(() => props.role, async (newRole) => {
    if (newRole && props.modelValue) {
        await loadPermissions();
    }
}, { immediate: true });

async function loadPermissions() {
    if (!props.role) return;
    loading.value = true;
    await rolesStore.loadRolePermissions(props.role.id);
    loading.value = false;
}

async function addPermission() {
    if (!props.role) return;
    loading.value = true;
    try {
        await rolesStore.editRolePermissions(props.role.id, {
            add: [{ resource_type: resourceType.value, resource_id: resourceId.value || 'default', permission: permission.value }],
            remove: []
        });
        ElMessage.success('Permission added');
    } catch (e) {
        ElMessage.error('Failed to add permission');
    } finally {
        loading.value = false;
    }
}

async function removePermission(p: RolePermission) {
    if (!props.role) return;
    try {
        await ElMessageBox.confirm(`Remove ${p.permission} on ${p.resource_type}:${p.resource_id}?`, 'Confirm', {
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
        });
        loading.value = true;
        await rolesStore.editRolePermissions(props.role.id, { add: [], remove: [p] });
        ElMessage.success('Permission removed');
    } catch (e) {
        if (e !== 'cancel') ElMessage.error('Failed to remove permission');
    } finally {
        loading.value = false;
    }
}

function handleClose() {
    emit('close');
}
</script>

<template>
  <el-dialog v-model="dialogVisible" title="Role Permissions" width="600px" :close-on-click-modal="false" @close="handleClose">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else>
      <el-table v-if="permissions.length" :data="permissions" style="width:100%">
        <el-table-column prop="resource_type" label="Resource" width="150" />
        <el-table-column prop="resource_id" label="ID" width="160" />
        <el-table-column prop="permission" label="Permission" />
        <el-table-column width="80">
          <template #default="scope">
            <el-button size="small" :icon="Delete" type="danger" @click="removePermission(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
      <div v-else class="empty-state">No permissions assigned.</div>
      <div class="add-section">
        <el-select v-model="resourceType" style="width:140px;margin-right:8px">
          <el-option label="Task" value="task" />
          <el-option label="Global" value="global_settings" />
        </el-select>
        <el-input v-model="resourceId" placeholder="Resource ID" style="width:140px;margin-right:8px" />
        <el-select v-model="permission" style="width:140px;margin-right:8px">
          <el-option v-for="opt in permOptions[resourceType]" :key="opt" :label="opt" :value="opt" />
        </el-select>
        <el-button type="primary" :icon="Plus" @click="addPermission" :loading="loading">Add</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.add-section { display:flex; margin-top:16px; }
.loading-container { margin:20px 0; }
.empty-state { text-align:center; margin:20px 0; }
</style>
