K:\Prog\enki-mr\ki\target\debug\deps\libki-9e1264a00ca98064.rmeta: src\main.rs src\api\mod.rs src\api\auth.rs src\api\routes.rs src\api\handlers\mod.rs src\api\handlers\firebase_handlers.rs src\api\handlers\task_handlers.rs src\api\handlers\user_handlers.rs src\api\handlers\agent_handlers.rs src\api\handlers\permission_handlers.rs src\api\handlers\role_handlers.rs src\api\handlers\sync_handlers.rs src\api\middleware\mod.rs src\api\middleware\zed_token.rs src\api\middleware\permission_check.rs src\db\mod.rs src\db\connection.rs src\db\models\mod.rs src\db\models\task.rs src\db\models\session.rs src\db\models\user.rs src\db\models\agent.rs src\db\models\permission.rs src\db\models\role.rs src\db\models\sync.rs src\db\init.rs src\db\repositories\mod.rs src\db\repositories\task_repository.rs src\db\repositories\session_repository.rs src\db\repositories\user_repository.rs src\db\repositories\agent_repository.rs src\db\repositories\permission_repository.rs src\db\repositories\role_repository.rs src\db\repositories\sync_repository.rs src\db\seed.rs src\services\mod.rs src\services\user_service.rs src\services\spicedb_service.rs src\services\sync_service.rs

K:\Prog\enki-mr\ki\target\debug\deps\ki-9e1264a00ca98064.d: src\main.rs src\api\mod.rs src\api\auth.rs src\api\routes.rs src\api\handlers\mod.rs src\api\handlers\firebase_handlers.rs src\api\handlers\task_handlers.rs src\api\handlers\user_handlers.rs src\api\handlers\agent_handlers.rs src\api\handlers\permission_handlers.rs src\api\handlers\role_handlers.rs src\api\handlers\sync_handlers.rs src\api\middleware\mod.rs src\api\middleware\zed_token.rs src\api\middleware\permission_check.rs src\db\mod.rs src\db\connection.rs src\db\models\mod.rs src\db\models\task.rs src\db\models\session.rs src\db\models\user.rs src\db\models\agent.rs src\db\models\permission.rs src\db\models\role.rs src\db\models\sync.rs src\db\init.rs src\db\repositories\mod.rs src\db\repositories\task_repository.rs src\db\repositories\session_repository.rs src\db\repositories\user_repository.rs src\db\repositories\agent_repository.rs src\db\repositories\permission_repository.rs src\db\repositories\role_repository.rs src\db\repositories\sync_repository.rs src\db\seed.rs src\services\mod.rs src\services\user_service.rs src\services\spicedb_service.rs src\services\sync_service.rs

src\main.rs:
src\api\mod.rs:
src\api\auth.rs:
src\api\routes.rs:
src\api\handlers\mod.rs:
src\api\handlers\firebase_handlers.rs:
src\api\handlers\task_handlers.rs:
src\api\handlers\user_handlers.rs:
src\api\handlers\agent_handlers.rs:
src\api\handlers\permission_handlers.rs:
src\api\handlers\role_handlers.rs:
src\api\handlers\sync_handlers.rs:
src\api\middleware\mod.rs:
src\api\middleware\zed_token.rs:
src\api\middleware\permission_check.rs:
src\db\mod.rs:
src\db\connection.rs:
src\db\models\mod.rs:
src\db\models\task.rs:
src\db\models\session.rs:
src\db\models\user.rs:
src\db\models\agent.rs:
src\db\models\permission.rs:
src\db\models\role.rs:
src\db\models\sync.rs:
src\db\init.rs:
src\db\repositories\mod.rs:
src\db\repositories\task_repository.rs:
src\db\repositories\session_repository.rs:
src\db\repositories\user_repository.rs:
src\db\repositories\agent_repository.rs:
src\db\repositories\permission_repository.rs:
src\db\repositories\role_repository.rs:
src\db\repositories\sync_repository.rs:
src\db\seed.rs:
src\services\mod.rs:
src\services\user_service.rs:
src\services\spicedb_service.rs:
src\services\sync_service.rs:
