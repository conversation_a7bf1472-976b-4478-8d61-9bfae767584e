use anyhow::Result;
use uuid::Uuid;
use crate::db::{Database, DatabaseConnection, models::{NewRole, BulkRelationshipOperation, RelationshipOperation, RelationshipTuple}};
use crate::services::spicedb_service::SpiceDBService;

/// Seed default roles if they don't exist
pub async fn seed_default_roles(db: &Database, spicedb: Option<&SpiceDBService>) -> Result<()> {
    tracing::info!("Checking for default roles...");

    // Check if any roles exist
    let has_roles = match &db.connection as &DatabaseConnection {
        #[cfg(feature = "sqlite")]
        DatabaseConnection::Sqlite(pool) => {
            let count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM roles")
                .fetch_one(pool)
                .await?;
            count > 0
        }
        #[cfg(feature = "postgres")]
        DatabaseConnection::Postgres(pool) => {
            let count = sqlx::query!("SELECT COUNT(*) as count FROM roles")
                .fetch_one(pool)
                .await?
                .count
                .unwrap_or(0);
            count > 0
        }
        #[allow(unreachable_patterns)]
        _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
    };

    if has_roles {
        tracing::info!("Roles already exist, skipping seeding");
        return Ok(());
    }

    tracing::info!("No roles found, creating default roles...");

    // Create default roles with proper IDs for SpiceDB integration
    let owner_role = NewRole {
        name: "Owner".to_string(),
        description: Some("Full system ownership with all administrative privileges".to_string()),
        color: Some("#dc2626".to_string()), // Red
    };

    let member_role = NewRole {
        name: "Member".to_string(),
        description: Some("Standard member access with basic permissions".to_string()),
        color: Some("#2563eb".to_string()), // Blue
    };

    // Insert roles in a transaction
    match &db.connection as &DatabaseConnection {
        #[cfg(feature = "sqlite")]
        DatabaseConnection::Sqlite(pool) => {
            let mut tx = pool.begin().await?;

            // Insert Owner role
            let owner_id = Uuid::new_v4();
            sqlx::query(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES (?, ?, ?, ?)
                "#
            )
            .bind(owner_id)
            .bind(&owner_role.name)
            .bind(&owner_role.description)
            .bind(&owner_role.color)
            .execute(&mut *tx)
            .await?;

            // Insert Member role
            let member_id = Uuid::new_v4();
            sqlx::query(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES (?, ?, ?, ?)
                "#
            )
            .bind(member_id)
            .bind(&member_role.name)
            .bind(&member_role.description)
            .bind(&member_role.color)
            .execute(&mut *tx)
            .await?;

            tx.commit().await?;

            tracing::info!("Created default roles: Owner ({}), Member ({})", owner_id, member_id);

            // Create SpiceDB relationships for default roles if SpiceDB service is available
            if let Some(spicedb_service) = spicedb {
                if let Err(e) = create_spicedb_relationships_for_roles(spicedb_service, &owner_id, &member_id).await {
                    tracing::warn!("Failed to create SpiceDB relationships for default roles: {}", e);
                    // Don't fail the seeding process if SpiceDB is unavailable
                }
            }
        }
        #[cfg(feature = "postgres")]
        DatabaseConnection::Postgres(pool) => {
            let mut tx = pool.begin().await?;

            // Insert Owner role
            let owner_id = Uuid::new_v4();
            sqlx::query!(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES ($1, $2, $3, $4)
                "#,
                owner_id,
                owner_role.name,
                owner_role.description,
                owner_role.color
            )
            .execute(&mut *tx)
            .await?;

            // Insert Member role
            let member_id = Uuid::new_v4();
            sqlx::query!(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES ($1, $2, $3, $4)
                "#,
                member_id,
                member_role.name,
                member_role.description,
                member_role.color
            )
            .execute(&mut *tx)
            .await?;

            tx.commit().await?;

            tracing::info!("Created default roles: Owner ({}), Member ({})", owner_id, member_id);

            // Create SpiceDB relationships for default roles if SpiceDB service is available
            if let Some(spicedb_service) = spicedb {
                if let Err(e) = create_spicedb_relationships_for_roles(spicedb_service, &owner_id, &member_id).await {
                    tracing::warn!("Failed to create SpiceDB relationships for default roles: {}", e);
                    // Don't fail the seeding process if SpiceDB is unavailable
                }
            }
        }

        #[allow(unreachable_patterns)]
        _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
    }

    tracing::info!("Default roles seeded successfully");
    Ok(())
}

/// Create SpiceDB relationships for default roles
async fn create_spicedb_relationships_for_roles(
    spicedb: &SpiceDBService,
    owner_role_id: &Uuid,
    member_role_id: &Uuid,
) -> Result<()> {
    tracing::info!("Creating SpiceDB relationships for default roles...");

    // Create relationships to grant Owner role comprehensive permissions
    let owner_relationships = vec![
        // Global settings permissions for Owner role
        RelationshipTuple {
            resource_type: "global_settings".to_string(),
            resource_id: "system".to_string(),
            relation: "admin_role".to_string(),
            subject_type: "role".to_string(),
            subject_id: "owner".to_string(), // Use fixed ID for SpiceDB
        },
        RelationshipTuple {
            resource_type: "global_settings".to_string(),
            resource_id: "system".to_string(),
            relation: "owner_role".to_string(),
            subject_type: "role".to_string(),
            subject_id: "owner".to_string(),
        },
    ];

    // Member role gets no global permissions by default
    // Members can only create and manage their own tasks through individual task permissions

    // Only create Owner role relationships
    let all_relationships = owner_relationships;

    if let Err(e) = spicedb.bulk_write_relationships(vec![
        BulkRelationshipOperation {
            operation: RelationshipOperation::Create,
            relationships: all_relationships,
        }
    ]).await {
        tracing::warn!("Failed to create default role permissions: {}", e);
    } else {
        tracing::info!("Created default role permissions: Owner and Member roles configured");
    }

    Ok(())
}
