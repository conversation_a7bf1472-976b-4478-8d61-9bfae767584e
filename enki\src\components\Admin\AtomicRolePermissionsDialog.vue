<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit, Check, Close } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import type { Role, RolePermission } from '@/types';

const props = defineProps<{ modelValue: boolean; role?: Role | null }>();
const emit = defineEmits<{ 'update:modelValue': [boolean]; 'close': [] }>();

const rolesStore = useRolesStore();

const dialogVisible = computed({
    get: () => props.modelValue,
    set: v => emit('update:modelValue', v)
});

const permissions = computed(() => {
    return props.role ? rolesStore.rolePermissions[props.role.id] || [] : [];
});

const loading = ref(false);
const saving = ref(false);

// Permission editing state
const pendingChanges = reactive({
    add: [] as RolePermission[],
    remove: [] as RolePermission[]
});

// Form state for adding new permissions
const newPermissionForm = reactive({
    resourceType: 'task' as 'task' | 'user' | 'agent' | 'role' | 'global_settings',
    resourceId: '',
    permission: 'view' as 'create' | 'view' | 'edit' | 'delete' | 'admin' | 'owner' | 'manage_users' | 'manage_agents' | 'manage_tasks' | 'manage_roles',
    isGlobal: false
});

// Resource type configurations
const resourceConfigs = {
    task: {
        label: 'Task',
        permissions: ['create', 'view', 'edit', 'delete', 'admin', 'owner'],
        globalPermission: 'manage_tasks',
        description: 'Task management permissions'
    },
    user: {
        label: 'User',
        permissions: ['create', 'view', 'edit', 'delete', 'admin'],
        globalPermission: 'manage_users',
        description: 'User management permissions'
    },
    agent: {
        label: 'Agent',
        permissions: ['create', 'view', 'edit', 'delete', 'admin'],
        globalPermission: 'manage_agents',
        description: 'Agent management permissions'
    },
    role: {
        label: 'Role',
        permissions: ['create', 'view', 'edit', 'delete', 'admin'],
        globalPermission: 'manage_roles',
        description: 'Role management permissions'
    },
    global_settings: {
        label: 'Global Settings',
        permissions: ['admin', 'owner', 'manage_users', 'manage_agents', 'manage_tasks', 'manage_roles'],
        globalPermission: null,
        description: 'System-wide permissions'
    }
};

const availablePermissions = computed(() => {
    return resourceConfigs[newPermissionForm.resourceType].permissions;
});

const canUseGlobal = computed(() => {
    return resourceConfigs[newPermissionForm.resourceType].globalPermission !== null;
});

watch(() => props.role, async (newRole) => {
    if (newRole && props.modelValue) {
        await loadPermissions();
        resetPendingChanges();
    }
}, { immediate: true });

watch(() => newPermissionForm.resourceType, () => {
    // Reset permission when resource type changes
    newPermissionForm.permission = availablePermissions.value[0] as any;
    newPermissionForm.resourceId = '';
    newPermissionForm.isGlobal = false;
});

watch(() => newPermissionForm.isGlobal, (isGlobal) => {
    if (isGlobal) {
        newPermissionForm.resourceId = 'system';
        newPermissionForm.permission = resourceConfigs[newPermissionForm.resourceType].globalPermission as any;
    } else {
        newPermissionForm.resourceId = '';
        newPermissionForm.permission = availablePermissions.value[0] as any;
    }
});

function resetPendingChanges() {
    pendingChanges.add = [];
    pendingChanges.remove = [];
}

async function loadPermissions() {
    if (!props.role) return;
    loading.value = true;
    await rolesStore.loadRolePermissions(props.role.id);
    loading.value = false;
}

function addToPending() {
    if (!props.role) return;
    
    const resourceId = newPermissionForm.isGlobal ? 'system' : newPermissionForm.resourceId;
    const permission = newPermissionForm.isGlobal 
        ? resourceConfigs[newPermissionForm.resourceType].globalPermission!
        : newPermissionForm.permission;

    if (!resourceId.trim()) {
        ElMessage.warning('Please enter a resource ID or use global permission');
        return;
    }

    const newPermission: RolePermission = {
        resource_type: newPermissionForm.resourceType,
        resource_id: resourceId,
        permission: permission
    };

    // Check if permission already exists or is already being added
    const exists = permissions.value.some(p => 
        p.resource_type === newPermission.resource_type &&
        p.resource_id === newPermission.resource_id &&
        p.permission === newPermission.permission
    );

    const alreadyAdding = pendingChanges.add.some(p => 
        p.resource_type === newPermission.resource_type &&
        p.resource_id === newPermission.resource_id &&
        p.permission === newPermission.permission
    );

    if (exists || alreadyAdding) {
        ElMessage.warning('This permission already exists or is being added');
        return;
    }

    pendingChanges.add.push(newPermission);
    
    // Reset form
    newPermissionForm.resourceId = '';
    newPermissionForm.isGlobal = false;
    
    ElMessage.success('Permission added to pending changes');
}

function removeFromPending(permission: RolePermission, isExisting: boolean) {
    if (isExisting) {
        // Remove from existing permissions
        const index = pendingChanges.remove.findIndex(p => 
            p.resource_type === permission.resource_type &&
            p.resource_id === permission.resource_id &&
            p.permission === permission.permission
        );
        
        if (index === -1) {
            pendingChanges.remove.push(permission);
            ElMessage.success('Permission marked for removal');
        } else {
            pendingChanges.remove.splice(index, 1);
            ElMessage.success('Permission removal cancelled');
        }
    } else {
        // Remove from pending additions
        const index = pendingChanges.add.findIndex(p => 
            p.resource_type === permission.resource_type &&
            p.resource_id === permission.resource_id &&
            p.permission === permission.permission
        );
        
        if (index !== -1) {
            pendingChanges.add.splice(index, 1);
            ElMessage.success('Pending permission removed');
        }
    }
}

function isMarkedForRemoval(permission: RolePermission): boolean {
    return pendingChanges.remove.some(p => 
        p.resource_type === permission.resource_type &&
        p.resource_id === permission.resource_id &&
        p.permission === permission.permission
    );
}

async function applyChanges() {
    if (!props.role) return;
    
    if (pendingChanges.add.length === 0 && pendingChanges.remove.length === 0) {
        ElMessage.warning('No changes to apply');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `Apply ${pendingChanges.add.length} additions and ${pendingChanges.remove.length} removals?`,
            'Confirm Changes',
            {
                type: 'warning',
                confirmButtonText: 'Apply Changes',
                cancelButtonText: 'Cancel'
            }
        );

        saving.value = true;
        
        await rolesStore.editRolePermissions(props.role.id, {
            add: pendingChanges.add,
            remove: pendingChanges.remove
        });
        
        resetPendingChanges();
        ElMessage.success('Permissions updated successfully');
        
    } catch (e) {
        if (e !== 'cancel') {
            ElMessage.error('Failed to update permissions');
        }
    } finally {
        saving.value = false;
    }
}

function cancelChanges() {
    if (pendingChanges.add.length > 0 || pendingChanges.remove.length > 0) {
        ElMessageBox.confirm('Discard all pending changes?', 'Confirm', {
            type: 'warning'
        }).then(() => {
            resetPendingChanges();
            ElMessage.success('Changes discarded');
        }).catch(() => {});
    }
}

function handleClose() {
    if (pendingChanges.add.length > 0 || pendingChanges.remove.length > 0) {
        ElMessageBox.confirm('You have unsaved changes. Close anyway?', 'Confirm', {
            type: 'warning'
        }).then(() => {
            resetPendingChanges();
            emit('close');
        }).catch(() => {});
    } else {
        emit('close');
    }
}

function getPermissionTypeColor(permission: string): string {
    const colors: Record<string, string> = {
        'view': 'info',
        'create': 'success',
        'edit': 'warning',
        'delete': 'danger',
        'admin': 'danger',
        'owner': 'danger',
        'manage_users': 'primary',
        'manage_agents': 'primary',
        'manage_tasks': 'primary',
        'manage_roles': 'primary'
    };
    return colors[permission] || 'info';
}

function getResourceTypeIcon(resourceType: string): string {
    const icons: Record<string, string> = {
        'task': '📝',
        'user': '👤',
        'agent': '🤖',
        'role': '👥',
        'global_settings': '⚙️'
    };
    return icons[resourceType] || '📄';
}
</script>

<template>
    <el-dialog 
        v-model="dialogVisible" 
        title="Atomic Role Permissions Management" 
        width="800px" 
        :close-on-click-modal="false" 
        @close="handleClose"
    >
        <div v-if="loading" class="loading-container">
            <el-skeleton :rows="4" animated />
        </div>
        
        <div v-else class="permissions-manager">
            <!-- Current Permissions -->
            <div class="section">
                <h4>Current Permissions</h4>
                <div v-if="permissions.length === 0 && pendingChanges.add.length === 0" class="empty-state">
                    No permissions assigned
                </div>
                
                <div v-else class="permissions-list">
                    <!-- Existing permissions -->
                    <div 
                        v-for="permission in permissions" 
                        :key="`existing-${permission.resource_type}-${permission.resource_id}-${permission.permission}`"
                        class="permission-item"
                        :class="{ 'marked-for-removal': isMarkedForRemoval(permission) }"
                    >
                        <div class="permission-info">
                            <span class="resource-icon">{{ getResourceTypeIcon(permission.resource_type) }}</span>
                            <div class="permission-details">
                                <div class="resource-info">
                                    <strong>{{ resourceConfigs[permission.resource_type as keyof typeof resourceConfigs]?.label || permission.resource_type }}</strong>
                                    <span class="resource-id">{{ permission.resource_id }}</span>
                                </div>
                                <el-tag 
                                    :type="getPermissionTypeColor(permission.permission)" 
                                    size="small"
                                >
                                    {{ permission.permission }}
                                </el-tag>
                            </div>
                        </div>
                        <el-button 
                            size="small" 
                            :type="isMarkedForRemoval(permission) ? 'success' : 'danger'"
                            :icon="isMarkedForRemoval(permission) ? Check : Delete"
                            @click="removeFromPending(permission, true)"
                        >
                            {{ isMarkedForRemoval(permission) ? 'Keep' : 'Remove' }}
                        </el-button>
                    </div>
                    
                    <!-- Pending additions -->
                    <div 
                        v-for="permission in pendingChanges.add" 
                        :key="`pending-${permission.resource_type}-${permission.resource_id}-${permission.permission}`"
                        class="permission-item pending-addition"
                    >
                        <div class="permission-info">
                            <span class="resource-icon">{{ getResourceTypeIcon(permission.resource_type) }}</span>
                            <div class="permission-details">
                                <div class="resource-info">
                                    <strong>{{ resourceConfigs[permission.resource_type as keyof typeof resourceConfigs]?.label || permission.resource_type }}</strong>
                                    <span class="resource-id">{{ permission.resource_id }}</span>
                                    <el-tag type="success" size="small">NEW</el-tag>
                                </div>
                                <el-tag 
                                    :type="getPermissionTypeColor(permission.permission)" 
                                    size="small"
                                >
                                    {{ permission.permission }}
                                </el-tag>
                            </div>
                        </div>
                        <el-button 
                            size="small" 
                            type="warning"
                            :icon="Close"
                            @click="removeFromPending(permission, false)"
                        >
                            Cancel
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- Add New Permission -->
            <div class="section">
                <h4>Add New Permission</h4>
                <div class="add-permission-form">
                    <div class="form-row">
                        <el-select v-model="newPermissionForm.resourceType" placeholder="Resource Type" style="width: 150px;">
                            <el-option 
                                v-for="(config, key) in resourceConfigs" 
                                :key="key"
                                :label="config.label" 
                                :value="key"
                            >
                                <span>{{ getResourceTypeIcon(key) }} {{ config.label }}</span>
                            </el-option>
                        </el-select>
                        
                        <el-checkbox 
                            v-if="canUseGlobal"
                            v-model="newPermissionForm.isGlobal"
                            label="Global Permission"
                        />
                    </div>
                    
                    <div class="form-row">
                        <el-input 
                            v-model="newPermissionForm.resourceId"
                            placeholder="Resource ID (e.g., task-123, user-456)"
                            style="width: 200px;"
                            :disabled="newPermissionForm.isGlobal"
                        />
                        
                        <el-select 
                            v-model="newPermissionForm.permission" 
                            placeholder="Permission" 
                            style="width: 150px;"
                            :disabled="newPermissionForm.isGlobal"
                        >
                            <el-option 
                                v-for="perm in availablePermissions" 
                                :key="perm"
                                :label="perm" 
                                :value="perm"
                            />
                        </el-select>
                        
                        <el-button 
                            type="primary" 
                            :icon="Plus" 
                            @click="addToPending"
                            :disabled="!newPermissionForm.resourceId.trim() && !newPermissionForm.isGlobal"
                        >
                            Add to Pending
                        </el-button>
                    </div>
                    
                    <div class="form-help">
                        <el-text type="info" size="small">
                            {{ resourceConfigs[newPermissionForm.resourceType].description }}
                        </el-text>
                    </div>
                </div>
            </div>

            <!-- Pending Changes Summary -->
            <div v-if="pendingChanges.add.length > 0 || pendingChanges.remove.length > 0" class="section">
                <h4>Pending Changes</h4>
                <div class="changes-summary">
                    <el-tag v-if="pendingChanges.add.length > 0" type="success">
                        +{{ pendingChanges.add.length }} additions
                    </el-tag>
                    <el-tag v-if="pendingChanges.remove.length > 0" type="danger">
                        -{{ pendingChanges.remove.length }} removals
                    </el-tag>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelChanges" :disabled="saving">
                    Cancel Changes
                </el-button>
                <el-button @click="handleClose" :disabled="saving">
                    Close
                </el-button>
                <el-button 
                    type="primary" 
                    @click="applyChanges"
                    :loading="saving"
                    :disabled="pendingChanges.add.length === 0 && pendingChanges.remove.length === 0"
                >
                    Apply Changes
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.permissions-manager {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
}

.loading-container {
    padding: 20px 0;
}

.empty-state {
    text-align: center;
    color: var(--el-text-color-secondary);
    padding: 20px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
}

.permissions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background: var(--el-bg-color);
    transition: all 0.3s ease;
}

.permission-item.marked-for-removal {
    background: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger-light-7);
    opacity: 0.7;
}

.permission-item.pending-addition {
    background: var(--el-color-success-light-9);
    border-color: var(--el-color-success-light-7);
}

.permission-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.resource-icon {
    font-size: 20px;
}

.permission-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.resource-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.resource-id {
    font-family: monospace;
    background: var(--el-fill-color-light);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.add-permission-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.form-help {
    margin-top: 4px;
}

.changes-summary {
    display: flex;
    gap: 8px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}
</style>
