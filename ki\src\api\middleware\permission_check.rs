use axum::{
    extract::{Request, State},
    http::{Method, StatusCode},
    middleware::Next,
    response::Response,
};
use axum_extra::extract::cookie::<PERSON><PERSON><PERSON>ar;
use tracing::{debug, warn};

use crate::{
    ServerState,
    db::repositories::SessionRepository,
    services::spicedb_service::SpiceDBService,
};

/// User context extracted from session
#[derive(Debug, <PERSON>lone)]
pub struct UserContext {
    pub user_id: String,
    pub firebase_user_id: String,
}

/// Permission requirement for a route
#[derive(Debug, Clone)]
pub struct PermissionRequirement {
    pub resource_type: String,
    pub permission: String,
    pub extract_resource_id: fn(&Request) -> Option<String>,
}

/// CRUD permission types
#[derive(Debug, Clone, Copy)]
pub enum CrudPermission {
    Create,
    Read,
    Update,
    Delete,
}

impl CrudPermission {
    pub fn as_str(&self) -> &'static str {
        match self {
            CrudPermission::Create => "create",
            CrudPermission::Read => "view",
            CrudPermission::Update => "edit",
            CrudPermission::Delete => "delete",
        }
    }
}

/// Extract user context from session cookie
pub async fn extract_user_context(
    state: &ServerState,
    jar: &CookieJar,
) -> Result<UserContext, StatusCode> {
    // Get session token from cookie
    let session_token = match jar.get("ki_session") {
        Some(cookie) => cookie.value().to_string(),
        None => {
            warn!("No session cookie found for permission check");
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    // Get session from database
    let session_repo = SessionRepository::new(state.db.clone());
    match session_repo.get_by_token(&session_token).await {
        Ok(Some(session)) => {
            // Check if session is expired
            if session.expires_at < chrono::Utc::now() {
                warn!("Session expired during permission check");
                return Err(StatusCode::UNAUTHORIZED);
            }

            Ok(UserContext {
                user_id: session.user_id.clone(),
                firebase_user_id: session.user_id, // In our system, user_id is the Firebase ID
            })
        }
        Ok(None) => {
            warn!("Invalid session token during permission check");
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(e) => {
            warn!("Error verifying session during permission check: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Check if user has permission for a specific resource
pub async fn check_permission(
    spicedb: &SpiceDBService,
    user_context: &UserContext,
    resource_type: &str,
    resource_id: &str,
    permission: &str,
) -> Result<bool, StatusCode> {
    match spicedb.check_permission(
        resource_type,
        resource_id,
        permission,
        &user_context.firebase_user_id,
    ).await {
        Ok(allowed) => {
            debug!(
                "Permission check: user={}, resource={}:{}, permission={}, allowed={}",
                user_context.firebase_user_id, resource_type, resource_id, permission, allowed
            );
            Ok(allowed)
        }
        Err(e) => {
            warn!("Error checking permission: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Check global permission (for resource creation, etc.)
pub async fn check_global_permission(
    spicedb: &SpiceDBService,
    user_context: &UserContext,
    resource_type: &str,
    _permission: &str,
) -> Result<bool, StatusCode> {
    // For global permissions, we check against global_settings
    check_permission(
        spicedb,
        user_context,
        "global_settings",
        "system",
        &format!("manage_{}", resource_type),
    ).await
}

/// Extract resource ID from path parameters
pub fn extract_resource_id_from_path(request: &Request, param_name: &str) -> Option<String> {
    // This is a simplified version - in practice, you'd use axum's path extraction
    // For now, we'll extract from the URI path
    let path = request.uri().path();
    let segments: Vec<&str> = path.split('/').collect();
    
    // Look for the parameter after the resource name
    // e.g., /api/users/{id} or /api/tasks/{id}
    for (i, segment) in segments.iter().enumerate() {
        if segment == &param_name && i + 1 < segments.len() {
            return Some(segments[i + 1].to_string());
        }
    }
    
    None
}

/// Create a permission checking middleware for specific CRUD operations
pub fn create_permission_middleware(
    resource_type: String,
    permission: CrudPermission,
) -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    move |state: State<ServerState>, jar: CookieJar, request: Request, next: Next| {
        let resource_type = resource_type.clone();
        let permission_str = permission.as_str().to_string();
        
        Box::pin(async move {
            // Extract user context
            let user_context = match extract_user_context(&state, &jar).await {
                Ok(ctx) => ctx,
                Err(status) => return Err(status),
            };

            // Get SpiceDB service
            let spicedb = match &state.spicedb {
                Some(service) => service,
                None => {
                    warn!("SpiceDB service not available for permission check");
                    return Err(StatusCode::SERVICE_UNAVAILABLE);
                }
            };

            // Determine if this is a resource-specific or global permission check
            let allowed = match request.method() {
                &Method::POST => {
                    // CREATE operations - check global permission
                    check_global_permission(spicedb, &user_context, &resource_type, &permission_str).await?
                }
                &Method::GET | &Method::PUT | &Method::DELETE => {
                    // READ/UPDATE/DELETE operations - check resource-specific permission
                    match extract_resource_id_from_path(&request, &resource_type) {
                        Some(resource_id) => {
                            // Resource-specific permission check
                            check_permission(spicedb, &user_context, &resource_type, &resource_id, &permission_str).await?
                        }
                        None => {
                            // If no resource ID, this might be a list operation - check global permission
                            check_global_permission(spicedb, &user_context, &resource_type, &permission_str).await?
                        }
                    }
                }
                _ => {
                    warn!("Unsupported HTTP method for permission check: {}", request.method());
                    return Err(StatusCode::METHOD_NOT_ALLOWED);
                }
            };

            if !allowed {
                warn!(
                    "Permission denied: user={}, resource={}:{}, permission={}",
                    user_context.firebase_user_id, resource_type, 
                    extract_resource_id_from_path(&request, &resource_type).unwrap_or_else(|| "global".to_string()),
                    permission_str
                );
                return Err(StatusCode::FORBIDDEN);
            }

            // Add user context to request extensions for use in handlers
            let mut request = request;
            request.extensions_mut().insert(user_context);

            // Permission granted, proceed to handler
            Ok(next.run(request).await)
        })
    }
}

/// Helper function to create middleware for different CRUD operations
pub fn users_create_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("users".to_string(), CrudPermission::Create)
}

pub fn users_read_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("users".to_string(), CrudPermission::Read)
}

pub fn users_update_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("users".to_string(), CrudPermission::Update)
}

pub fn users_delete_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("users".to_string(), CrudPermission::Delete)
}

pub fn agents_create_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("agents".to_string(), CrudPermission::Create)
}

pub fn agents_read_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("agents".to_string(), CrudPermission::Read)
}

pub fn agents_update_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("agents".to_string(), CrudPermission::Update)
}

pub fn agents_delete_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("agents".to_string(), CrudPermission::Delete)
}

pub fn roles_create_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("roles".to_string(), CrudPermission::Create)
}

pub fn roles_read_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("roles".to_string(), CrudPermission::Read)
}

pub fn roles_update_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("roles".to_string(), CrudPermission::Update)
}

pub fn roles_delete_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("roles".to_string(), CrudPermission::Delete)
}

pub fn tasks_create_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("tasks".to_string(), CrudPermission::Create)
}

pub fn tasks_read_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("tasks".to_string(), CrudPermission::Read)
}

pub fn tasks_update_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("tasks".to_string(), CrudPermission::Update)
}

pub fn tasks_delete_permission() -> impl Fn(State<ServerState>, CookieJar, Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    create_permission_middleware("tasks".to_string(), CrudPermission::Delete)
}
