<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Delete, User, UserFilled } from '@element-plus/icons-vue';
import { usePermissions } from '@/composables/usePermissions';
import { useUsersStore } from '@/stores/usersStore';
import { useRolesStore } from '@/stores/rolesStore';
import type { Task } from '@/types';

const props = defineProps<{
    task: Task;
}>();

const emit = defineEmits<{
    'permissions-updated': [];
}>();

const { 
    setTaskPermission, 
    removeTaskPermission, 
    canEditTask,
    loading: permissionsLoading 
} = usePermissions();

const usersStore = useUsersStore();
const rolesStore = useRolesStore();

// State
const loading = ref(false);
const showAddPermissionDialog = ref(false);
const selectedSubjectType = ref<'user' | 'role'>('user');
const selectedSubjectId = ref('');
const selectedPermission = ref<'owner' | 'viewer' | 'editor' | 'admin'>('viewer');

// Mock current permissions (in real implementation, this would come from API)
const currentPermissions = ref<Array<{
    id: string;
    permission: 'owner' | 'viewer' | 'editor' | 'admin';
    subjectType: 'user' | 'role';
    subjectId: string;
    subjectName: string;
    subjectAvatar?: string;
}>>([]);

// Computed properties
const canEdit = ref(false);

const availableUsers = computed(() => usersStore.users);
const availableRoles = computed(() => rolesStore.roles);

const permissionOptions = [
    { value: 'viewer', label: 'Viewer', description: 'Can view the task' },
    { value: 'editor', label: 'Editor', description: 'Can view and edit the task' },
    { value: 'admin', label: 'Admin', description: 'Can view, edit, and manage permissions' },
    { value: 'owner', label: 'Owner', description: 'Full control over the task' }
];

// const selectedSubject = computed(() => {
//     if (selectedSubjectType.value === 'user') {
//         return availableUsers.value.find(u => u.id === selectedSubjectId.value);
//     } else {
//         return availableRoles.value.find(r => r.id === selectedSubjectId.value);
//     }
// });

// Methods
onMounted(async () => {
    // Check if user can edit this task
    canEdit.value = await canEditTask(props.task.id);
    
    // Load users and roles - TODO : Maybe we should do it from init
    await rolesStore.loadRoles();
    
    // Load current permissions (placeholder - in real implementation, fetch from API)
    loadCurrentPermissions();
});

const loadCurrentPermissions = () => {
    // This is a placeholder. In real implementation, you would fetch actual permissions from the API
    // For now, we'll show some mock data based on task assignees
    currentPermissions.value = props.task.assignees.map((assigneeId, index) => {
        const user = usersStore.users.find(u => u.id === assigneeId);
        return {
            id: `perm-${index}`,
            permission: 'editor' as const,
            subjectType: 'user' as const,
            subjectId: assigneeId,
            subjectName: user?.display_name || user?.email || 'Unknown User',
            subjectAvatar: user?.photo_url
        };
    });
};

const openAddPermissionDialog = () => {
    selectedSubjectType.value = 'user';
    selectedSubjectId.value = '';
    selectedPermission.value = 'viewer';
    showAddPermissionDialog.value = true;
};

const handleAddPermission = async () => {
    if (!selectedSubjectId.value) {
        ElMessage.warning('Please select a user or role');
        return;
    }
    
    loading.value = true;
    try {
        const success = await setTaskPermission(
            props.task.id,
            selectedPermission.value,
            selectedSubjectType.value,
            selectedSubjectId.value
        );
        
        if (success) {
            ElMessage.success('Permission added successfully');
            loadCurrentPermissions(); // Reload permissions
            showAddPermissionDialog.value = false;
            emit('permissions-updated');
        }
    } catch (error) {
        console.error('Failed to add permission:', error);
        ElMessage.error('Failed to add permission');
    } finally {
        loading.value = false;
    }
};

const removePermission = async (permission: typeof currentPermissions.value[0]) => {
    loading.value = true;
    try {
        const success = await removeTaskPermission(
            props.task.id,
            permission.permission,
            permission.subjectType,
            permission.subjectId
        );
        
        if (success) {
            ElMessage.success('Permission removed successfully');
            loadCurrentPermissions(); // Reload permissions
            emit('permissions-updated');
        }
    } catch (error) {
        console.error('Failed to remove permission:', error);
        ElMessage.error('Failed to remove permission');
    } finally {
        loading.value = false;
    }
};

const getSubjectAvatar = (permission: typeof currentPermissions.value[0]) => {
    if (permission.subjectAvatar) return permission.subjectAvatar;
    
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(permission.subjectName)}&background=random`;
};

const getPermissionColor = (permission: string) => {
    const colors = {
        viewer: '#10b981',
        editor: '#3b82f6', 
        admin: '#f59e0b',
        owner: '#ef4444'
    };
    return colors[permission as keyof typeof colors] || '#6b7280';
};
</script>

<template>
    <el-card class="permissions-panel">
        <template #header>
            <div class="panel-header">
                <div class="header-info">
                    <h4>Permissions</h4>
                    <p class="header-description">
                        Manage who can access this task
                    </p>
                </div>
                <el-button 
                    v-if="canEdit"
                    type="primary" 
                    size="small"
                    :icon="Plus" 
                    @click="openAddPermissionDialog"
                    :loading="loading || permissionsLoading"
                >
                    Add Permission
                </el-button>
            </div>
        </template>
        
        <div class="permissions-content">
            <div v-if="currentPermissions.length === 0" class="empty-state">
                <p>No specific permissions set. Task inherits default permissions.</p>
                <el-button 
                    v-if="canEdit"
                    type="primary" 
                    @click="openAddPermissionDialog"
                >
                    Add First Permission
                </el-button>
            </div>
            
            <div v-else class="permissions-list">
                <div 
                    v-for="permission in currentPermissions" 
                    :key="permission.id"
                    class="permission-item"
                >
                    <div class="permission-info">
                        <el-avatar 
                            :src="getSubjectAvatar(permission)"
                            :size="32"
                        >
                            <el-icon>
                                <User v-if="permission.subjectType === 'user'" />
                                <UserFilled v-else />
                            </el-icon>
                        </el-avatar>
                        <div class="permission-details">
                            <div class="subject-name">{{ permission.subjectName }}</div>
                            <div class="permission-level">
                                <el-tag 
                                    :color="getPermissionColor(permission.permission)"
                                    effect="light"
                                    size="small"
                                >
                                    {{ permission.permission }}
                                </el-tag>
                            </div>
                        </div>
                    </div>
                    <el-button 
                        v-if="canEdit"
                        :icon="Delete" 
                        type="danger" 
                        size="small"
                        @click="removePermission(permission)"
                        :loading="loading"
                    />
                </div>
            </div>
        </div>
    </el-card>
    
    <!-- Add Permission Dialog -->
    <el-dialog
        v-model="showAddPermissionDialog"
        title="Add Permission"
        width="400px"
        :close-on-click-modal="false"
    >
        <el-form label-width="100px">
            <el-form-item label="Type">
                <el-radio-group v-model="selectedSubjectType">
                    <el-radio label="user">User</el-radio>
                    <el-radio label="role">Role</el-radio>
                </el-radio-group>
            </el-form-item>
            
            <el-form-item :label="selectedSubjectType === 'user' ? 'User' : 'Role'">
                <el-select 
                    v-model="selectedSubjectId" 
                    placeholder="Select..."
                    style="width: 100%"
                    filterable
                >
                    <template v-if="selectedSubjectType === 'user'">
                        <el-option
                            v-for="user in availableUsers"
                            :key="user.id"
                            :label="user.display_name || user.email"
                            :value="user.id"
                        >
                            <div class="option-content">
                                <el-avatar :src="user.photo_url" :size="20">
                                    <el-icon><User /></el-icon>
                                </el-avatar>
                                <span>{{ user.display_name || user.email }}</span>
                            </div>
                        </el-option>
                    </template>
                    <template v-else>
                        <el-option
                            v-for="role in availableRoles"
                            :key="role.id"
                            :label="role.name"
                            :value="role.id"
                        >
                            <div class="option-content">
                                <div 
                                    class="role-color-indicator"
                                    :style="{ backgroundColor: role.color || '#6b7280' }"
                                ></div>
                                <span>{{ role.name }}</span>
                            </div>
                        </el-option>
                    </template>
                </el-select>
            </el-form-item>
            
            <el-form-item label="Permission">
                <el-select v-model="selectedPermission" style="width: 100%">
                    <el-option
                        v-for="option in permissionOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    >
                        <div class="permission-option">
                            <div class="permission-label">{{ option.label }}</div>
                            <div class="permission-description">{{ option.description }}</div>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showAddPermissionDialog = false">Cancel</el-button>
                <el-button 
                    type="primary" 
                    @click="handleAddPermission"
                    :disabled="!selectedSubjectId"
                    :loading="loading"
                >
                    Add Permission
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.permissions-panel {
    margin-top: 16px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-info h4 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
}

.header-description {
    margin: 0;
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.permissions-content {
    min-height: 100px;
}

.empty-state {
    text-align: center;
    padding: 24px;
    color: var(--el-text-color-regular);
}

.permissions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.permission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    background: var(--el-bg-color);
}

.permission-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.permission-details {
    flex: 1;
}

.subject-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
}

.permission-level {
    font-size: 12px;
}

.option-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.role-color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.permission-option {
    display: flex;
    flex-direction: column;
}

.permission-label {
    font-weight: 500;
}

.permission-description {
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
