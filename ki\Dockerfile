FROM rust:1.86-alpine3.21 AS builder
WORKDIR /app
COPY . /app/
ENV DATABASE_URL=sqlite:/app/data/ki.db
RUN --mount=type=cache,target=/usr/local/cargo/git \
    --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,sharing=private,target=/app/target \
    apk --no-cache add build-base musl-dev openssl-dev openssl-libs-static ca-certificates && \
    cargo build --release --features sqlite && \
    cp -v target/release/ki /ki

FROM scratch
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /ki /ki
COPY --from=builder /app/schema.zed /schema.zed
CMD [ "/ki" ]